// AUTHENTICATION CHECK - Ensure user is logged in and not suspended
async function checkAuthentication() {
    console.log('🔐 Checking farmer authentication and account status...');

    // Check for authentication token
    const token = localStorage.getItem('farmerToken') || localStorage.getItem('authToken');
    const userType = localStorage.getItem('userType');
    const userId = localStorage.getItem('userId');

    if (!token) {
        console.warn('❌ No authentication token found');
        return false;
    }

    if (userType !== 'Farmer' && userType !== 'farmer') {
        console.warn('❌ User is not a farmer:', userType);
        return false;
    }

    if (!userId) {
        console.warn('❌ No user ID found');
        return false;
    }

    // REAL SYSTEM CHECK: Verify account status with backend
    try {
        const response = await fetch('/api/farmer/profile/', {
            headers: {
                'Authorization': `Token ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.status === 401) {
            console.warn('❌ Token invalid or expired');
            localStorage.clear();
            return false;
        }

        if (response.ok) {
            const data = await response.json();
            const user = data.farmer || data.user;

            // Check if account is suspended
            if (!user.is_active) {
                console.warn('❌ Account is suspended');
                showSuspensionMessage();
                return false;
            }

            // Check if farmer is approved
            if (!user.is_approved) {
                console.warn('⏳ Account pending approval');
                showPendingApprovalMessage();
                return false;
            }

            console.log('✅ Authentication and account status verified for farmer:', userId);
            return true;
        } else {
            console.warn('❌ Failed to verify account status');
            return false;
        }
    } catch (error) {
        console.error('❌ Error checking account status:', error);
        return false;
    }
}

// Show suspension message
function showSuspensionMessage() {
    document.body.innerHTML = `
        <div class="suspension-screen">
            <div class="suspension-container">
                <div class="suspension-icon">
                    <i class="fas fa-ban"></i>
                </div>
                <h1>Account Suspended</h1>
                <p>Your account has been suspended by the administrator.</p>
                <p>You cannot access the farmer dashboard at this time.</p>
                <div class="suspension-details">
                    <h3>What this means:</h3>
                    <ul>
                        <li>You cannot access your dashboard</li>
                        <li>You cannot manage your products</li>
                        <li>You cannot view reservations</li>
                    </ul>
                </div>
                <div class="contact-info">
                    <h3>Need help?</h3>
                    <p>Contact our support team:</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +************</p>
                </div>
                <button onclick="logout()" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
        <style>
            .suspension-screen {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: Arial, sans-serif;
            }
            .suspension-container {
                background: white;
                padding: 3rem;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 500px;
                margin: 2rem;
            }
            .suspension-icon {
                font-size: 4rem;
                color: #e74c3c;
                margin-bottom: 1rem;
            }
            .suspension-container h1 {
                color: #2c3e50;
                margin-bottom: 1rem;
            }
            .suspension-details, .contact-info {
                text-align: left;
                margin: 2rem 0;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: 8px;
            }
            .logout-btn {
                background: #e74c3c;
                color: white;
                border: none;
                padding: 1rem 2rem;
                border-radius: 8px;
                cursor: pointer;
                font-size: 1rem;
                margin-top: 1rem;
            }
            .logout-btn:hover {
                background: #c0392b;
            }
        </style>
    `;
}

// Show pending approval message
function showPendingApprovalMessage() {
    document.body.innerHTML = `
        <div class="pending-screen">
            <div class="pending-container">
                <div class="pending-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h1>Application Pending</h1>
                <p>Your farmer application is currently under review by our administrators.</p>
                <div class="pending-details">
                    <h3>What happens next:</h3>
                    <ul>
                        <li>Our team will review your application</li>
                        <li>You'll receive an email notification once approved</li>
                        <li>You'll then be able to access your dashboard</li>
                    </ul>
                </div>
                <div class="contact-info">
                    <h3>Questions?</h3>
                    <p>Contact our support team:</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Phone:</strong> +************</p>
                </div>
                <button onclick="logout()" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
        <style>
            .pending-screen {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                font-family: Arial, sans-serif;
            }
            .pending-container {
                background: white;
                padding: 3rem;
                border-radius: 15px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 500px;
                margin: 2rem;
            }
            .pending-icon {
                font-size: 4rem;
                color: #f39c12;
                margin-bottom: 1rem;
            }
            .pending-container h1 {
                color: #2c3e50;
                margin-bottom: 1rem;
            }
            .pending-details, .contact-info {
                text-align: left;
                margin: 2rem 0;
                padding: 1rem;
                background: #f8f9fa;
                border-radius: 8px;
            }
            .logout-btn {
                background: #f39c12;
                color: white;
                border: none;
                padding: 1rem 2rem;
                border-radius: 8px;
                cursor: pointer;
                font-size: 1rem;
                margin-top: 1rem;
            }
            .logout-btn:hover {
                background: #e67e22;
            }
        </style>
    `;
}

// Logout function
function logout() {
    localStorage.clear();
    window.location.href = 'loginfarmer.html';
}

// Global content loader function
function loadContent(page) {
    console.log('Loading content for page:', page);
    const contentDiv = document.getElementById('dashboardContent');

    if (!contentDiv) {
        console.error('Dashboard content div not found!');
        return;
    }

    // Show loading indicator
    contentDiv.innerHTML = '<div class="loading">Loading...</div>';

    // Map page names to actual content files
    const pageMapping = {
        'profile': 'farmerprofile',
        'farmerprofile': 'farmerprofile',
        'dashboard': 'dashboard',
        'mylisting': 'mylisting',
        'addproduct': 'addproduct',
        'addurgentsale': 'addurgentsale',
        'editlisting': 'editlisting',
        'reservations': 'reservations',
        'settings': 'settings'
    };

    const actualPage = pageMapping[page] || page;
    console.log('Mapped page name:', actualPage);

    // Check if running on file:// protocol
    if (window.location.protocol === 'file:') {
        console.warn('Running on file:// protocol - using placeholder content');
        contentDiv.innerHTML = `
            <div class="placeholder-content">
                <div class="placeholder-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="placeholder-text">
                    <h3>Building Real Dynamic System</h3>
                    <p>This page will be built with real database integration.</p>
                    <p><strong>Page:</strong> ${page}</p>
                    <p class="error-details">Static content removed - building real functionality</p>
                    <div class="placeholder-actions">
                        <button onclick="loadContent('dashboard')" class="btn-primary">
                            <i class="fas fa-home"></i> Back to Dashboard
                        </button>
                    </div>
                </div>
            </div>
        `;
        return;
    }

    // Load content based on page
    if (actualPage === 'dashboard') {
        loadDashboardContent();
    } else {
        // For other pages, try to load from separate files or show placeholder
        loadPageContent(actualPage);
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 Farmer Dashboard - Initializing...');

    // CRITICAL: Check authentication and account status before loading dashboard
    const isAuthenticated = await checkAuthentication();
    if (!isAuthenticated) {
        console.warn('❌ User not authenticated or account suspended, redirecting to login');
        window.location.href = 'loginfarmer.html';
        return;
    }

    loadFarmerProfile();
    console.log('Dashboard loaded successfully');

    // Set document title
    document.title = "Farmer Dashboard - AGRIPORT";

    // Initialize header functionality
    setupHeader();

    // Initialize messaging system
    setupMessaging();

    // Update badges periodically
    updateNotificationBadge();
    setInterval(updateNotificationBadge, 30000); // Update every 30 seconds
    
    // Basic sidebar toggle
    const toggleSidebar = document.getElementById('toggleSidebar');
    const sidebar = document.getElementById('sidebar');
    
    if (toggleSidebar && sidebar) {
        toggleSidebar.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
        });
    }
    
    // Mobile menu toggle
    const mobileMenuToggle = document.getElementById('mobileMenuToggle');
    
    if (mobileMenuToggle && sidebar) {
        mobileMenuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
    }
    
    // Handle navigation links
    const navLinks = document.querySelectorAll('.nav-menu a');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Don't prevent default for logout
            if (this.id === 'logoutBtn') {
                return;
            }
            
            e.preventDefault();
            
            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Get the href attribute (without the #)
            const page = this.getAttribute('href').substring(1);
            
            // Load content
            loadContent(page);

            // Update page title
            updatePageTitle();

            // On mobile, close the sidebar after clicking
            if (window.innerWidth < 768) {
                sidebar.classList.remove('show');
            }
        });
    });
    
    // Simple content loader
    function loadContent(page) {
        console.log('Loading content for page:', page);
        const contentDiv = document.getElementById('dashboardContent');

        if (!contentDiv) {
            console.error('Dashboard content div not found!');
            return;
        }

        // Show loading indicator
        contentDiv.innerHTML = '<div class="loading">Loading...</div>';

        // Map page names to actual content files
        const pageMapping = {
            'profile': 'farmerprofile',
            'farmerprofile': 'farmerprofile',
            'dashboard': 'dashboard',
            'mylisting': 'mylisting',
            'addproduct': 'addproduct',
            'addurgentsale': 'addurgentsale',
            'editlisting': 'editlisting',
            'reservations': 'reservations',
            'settings': 'settings'
        };

        const actualPage = pageMapping[page] || page;
        console.log('Mapped page name:', actualPage);

        // Check if running on file:// protocol
        if (window.location.protocol === 'file:') {
            console.warn('Running on file:// protocol - using inline content');
            loadInlineContent(actualPage, contentDiv);
            return;
        }

        console.log('Attempting to load:', `${actualPage}-content.html`);

        // Load the content via fetch (for http/https)
        fetch(`${actualPage}-content.html`)
            .then(response => {
                console.log('Fetch response status:', response.status);
                if (!response.ok) {
                    throw new Error(`Content file not found: ${actualPage}-content.html (Status: ${response.status})`);
                }
                return response.text();
            })
            .then(html => {
                console.log('Content loaded successfully, length:', html.length);
                contentDiv.innerHTML = html;
                
                // Initialize specific functionality based on the loaded page
                if (page === 'profile') {
                    // Map profile to farmerprofile for content loading
                    initializeProfileTabs();
                } else if (page === 'farmerprofile') {
                    initializeProfileTabs();
                } else if (page === 'addproduct') {
                    loadFarmerCategories();
                    initializeCategoryRequestModal();
                    initializeImagePreview();
                    initializeProductForm();
                } else if (page === 'addurgentsale') {
                    // Initialize urgent sale functionality
                    initializeUrgentSaleForm();
                } else if (page === 'dashboard') {
                    // Load dashboard data including urgent sales
                    loadDashboardData();
                    // Setup dashboard action buttons
                    setupDashboardActionButtons();
                } else if (page === 'mylisting') {
                    // Load product listings including urgent sales
                    loadProductListings();
                }
            })
            .catch(error => {
                console.error('Error loading content:', error);
                console.error('Failed to load:', `${actualPage}-content.html`);

                // Show message that real content needs to be built
                contentDiv.innerHTML = `
                    <div class="content-placeholder">
                        <div class="placeholder-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h3>Building Real Dynamic System</h3>
                        <p>This page will be built with real database integration.</p>
                        <p><strong>Page:</strong> ${page}</p>
                        <p class="error-details">Static content removed - building real functionality</p>
                        <div class="placeholder-actions">
                            <button onclick="loadContent('dashboard')" class="btn-primary">
                                <i class="fas fa-home"></i> Back to Dashboard
                            </button>
                        </div>
                    </div>
                `;
            });
    }

    // Load inline content for file:// protocol with REAL dynamic templates
    function loadInlineContent(page, contentDiv) {
        console.log('Loading inline content for:', page);

        switch(page) {
            case 'dashboard':
                contentDiv.innerHTML = `
                    <div class="main-content">
                        <div class="dashboard-header">
                            <h1>Dashboard Overview</h1>
                            <p>Welcome back! Here's what's happening with your farm.</p>
                        </div>

                        <!-- Quick Stats -->
                        <div class="quick-stats">
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="stat-details">
                                    <div class="number">0</div>
                                    <div class="label">Active Listings</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="stat-details">
                                    <div class="number">0</div>
                                    <div class="label">Pending Reservations</div>
                                </div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="stat-details">
                                    <div class="number">0</div>
                                    <div class="label">Monthly Revenue</div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Reservations with BUYER NAMES -->
                        <div class="dashboard-section">
                            <div class="section-header">
                                <h2><i class="fas fa-calendar-check"></i> Recent Reservations</h2>
                                <button onclick="loadContent('reservations')" class="view-all-btn">
                                    <i class="fas fa-arrow-right"></i> View All
                                </button>
                            </div>
                            <div class="recent-reservations">
                                <div class="loading">
                                    <i class="fas fa-spinner fa-spin"></i> Loading buyer reservations...
                                </div>
                            </div>
                        </div>

                        <!-- Product Listings -->
                        <div class="dashboard-section">
                            <div class="section-header">
                                <h2><i class="fas fa-seedling"></i> Your Products</h2>
                                <button onclick="loadContent('addproduct')" class="add-btn">
                                    <i class="fas fa-plus"></i> Add Product
                                </button>
                            </div>
                            <div class="product-list">
                                <div class="loading">
                                    <i class="fas fa-spinner fa-spin"></i> Loading your products...
                                </div>
                            </div>
                        </div>

                        <!-- Urgent Sales -->
                        <div class="dashboard-section">
                            <div class="section-header">
                                <h2><i class="fas fa-fire"></i> Urgent Sales</h2>
                                <button onclick="loadContent('addurgentsale')" class="add-btn">
                                    <i class="fas fa-plus"></i> Create Urgent Sale
                                </button>
                            </div>
                            <div class="urgent-sales-list">
                                <div class="loading">
                                    <i class="fas fa-spinner fa-spin"></i> Loading urgent sales...
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Load REAL dashboard data after template is loaded
                setTimeout(() => {
                    loadDashboardData();
                }, 100);
                break;

            case 'mylisting':
                loadMyListingsPage(contentDiv);
                break;

            case 'addproduct':
                loadAddProductPage(contentDiv);
                break;

            case 'addurgentsale':
                loadAddUrgentSalePage(contentDiv);
                break;

            case 'editlisting':
                loadEditListingPage(contentDiv);
                break;

            case 'reservations':
                loadReservationsPage(contentDiv);
                break;

            case 'saleshistory':
                loadSalesHistoryPage(contentDiv);
                break;

            case 'profile':
                loadProfilePage(contentDiv);
                break;

            case 'settings':
                loadSettingsPage(contentDiv);
                break;

            default:
                contentDiv.innerHTML = `
                    <div class="main-content">
                        <div class="page-header">
                            <h1>${page.charAt(0).toUpperCase() + page.slice(1)}</h1>
                        </div>
                        <div class="content-placeholder">
                            <i class="fas fa-tools"></i>
                            <h3>Content Under Development</h3>
                            <p>This page is being built with real functionality.</p>
                        </div>
                    </div>
                `;
                break;
        }

        // Initialize page-specific functionality
        if (page === 'dashboard') {
            setupDashboardActionButtons();
        }
    }

    // Load default dashboard content
    const hash = window.location.hash.substring(1);
    if (hash) {
        // Find and activate the link
        const link = document.querySelector(`.nav-menu a[href="#${hash}"]`);
        if (link) {
            link.click();
        } else {
            loadContent('dashboard');
        }
    } else {
        loadContent('dashboard');
    }
});

// Function to handle category request modal
function initializeCategoryRequestModal() {
    const requestCategoryBtn = document.getElementById('requestCategoryBtn');
    const categoryRequestModal = document.getElementById('categoryRequestModal');
    const closeModalBtns = document.querySelectorAll('.close-modal');
    
    if (requestCategoryBtn && categoryRequestModal) {
        // Open modal
        requestCategoryBtn.addEventListener('click', function() {
            categoryRequestModal.style.display = 'block';
            document.body.classList.add('modal-open');
        });
        
        // Close modal
        closeModalBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                categoryRequestModal.style.display = 'none';
                document.body.classList.remove('modal-open');
            });
        });
        
        // Close when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === categoryRequestModal) {
                categoryRequestModal.style.display = 'none';
                document.body.classList.remove('modal-open');
            }
        });
        
        // Handle form submission
        const categoryRequestForm = document.getElementById('categoryRequestForm');
        if (categoryRequestForm) {
            categoryRequestForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Get form data
                const newCategory = document.getElementById('newCategory').value;
                const categoryDescription = document.getElementById('categoryDescription').value;
                const reasonForRequest = document.getElementById('reasonForRequest').value;
                
                // Show saving indicator
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn.textContent;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
                submitBtn.disabled = true;
                
                // Simulate API call to submit category request
                setTimeout(() => {
                    // Reset button
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    
                    // Close modal
                    categoryRequestModal.style.display = 'none';
                    document.body.classList.remove('modal-open');
                    
                    // Add to pending requests (in a real app, this would come from the server)
                    const pendingRequests = document.querySelector('.pending-requests');
                    const newRequest = document.createElement('div');
                    newRequest.className = 'pending-category';
                    newRequest.innerHTML = `
                        <span>${newCategory}</span>
                        <span class="status-badge pending">Pending Approval</span>
                    `;
                    pendingRequests.appendChild(newRequest);
                    
                    // Show success message
                    showNotification('Category request submitted successfully! Admin will review your request.', 'success');
                    
                    // Reset form
                    categoryRequestForm.reset();
                }, 1500);
            });
        }
    }
}

// Function to load farmer's approved categories
function loadFarmerCategories() {
    // In a real app, this would be an API call to get the farmer's approved categories
    // For now, we'll simulate it
    
    const categorySelect = document.getElementById('category');
    if (!categorySelect) return;
    
    // Clear existing options except the first one
    while (categorySelect.options.length > 1) {
        categorySelect.remove(1);
    }
    
    // Simulate loading categories from API
    const farmerCategories = [
        { id: 1, name: 'Vegetables' },
        { id: 2, name: 'Fruits' },
        { id: 3, name: 'Tubers' }
    ];
    
    // Add options to select
    farmerCategories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.id;
        option.textContent = category.name;
        categorySelect.appendChild(option);
    });
    
    // Update approved categories display
    const approvedCategoriesDiv = document.querySelector('.approved-categories');
    if (approvedCategoriesDiv) {
        approvedCategoriesDiv.innerHTML = '';
        farmerCategories.forEach(category => {
            const categoryTag = document.createElement('div');
            categoryTag.className = 'category-tag';
            categoryTag.textContent = category.name;
            approvedCategoriesDiv.appendChild(categoryTag);
        });
    }
}

// Function to handle product image preview
function initializeImagePreview() {
    const productImageInput = document.getElementById('productImage');
    const imagePreview = document.getElementById('imagePreview');
    
    if (productImageInput && imagePreview) {
        productImageInput.addEventListener('change', function() {
            imagePreview.innerHTML = '';
            
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'preview-image';
                    imagePreview.appendChild(img);
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
}

// Function to update quantity fields based on selected unit
function updateQuantityFields() {
    const unitSelect = document.getElementById('unit');
    const weightBasedFields = document.getElementById('weightBasedFields');
    const countBasedFields = document.getElementById('countBasedFields');
    
    if (!unitSelect || !weightBasedFields || !countBasedFields) return;
    
    // Hide all unit-specific fields first
    weightBasedFields.style.display = 'none';
    countBasedFields.style.display = 'none';
    
    // Show relevant fields based on unit type
    const selectedUnit = unitSelect.value;
    
    if (selectedUnit === 'sack' || selectedUnit === 'basket') {
        // Volume-based units might need weight specification
        weightBasedFields.style.display = 'block';
    } else if (selectedUnit === 'bunch') {
        // Count-based units might need item count specification
        countBasedFields.style.display = 'block';
    }
}

// Initialize fields when page loads
document.addEventListener('DOMContentLoaded', function() {
    const unitSelect = document.getElementById('unit');
    if (unitSelect) {
        updateQuantityFields();
        
        // Add change event listener
        unitSelect.addEventListener('change', updateQuantityFields);
    }
    
    // Add product form submission handler
    const addProductForm = document.getElementById('addProductForm');
    if (addProductForm) {
        addProductForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = addProductForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            submitBtn.disabled = true;
            
            // Collect form data including structured quantity information
            const formData = new FormData(addProductForm);
            const productData = {
                name: formData.get('productName'),
                category: formData.get('category'),
                price: formData.get('price'),
                unit: formData.get('unit'),
                quantity: formData.get('quantity'),
                description: formData.get('description'),
                location: formData.get('location'),
                harvestDate: formData.get('harvestDate') || null
            };
            
            // Add unit-specific data if present
            if (formData.get('weightPerUnit')) {
                productData.weightPerUnit = formData.get('weightPerUnit');
            }
            
            if (formData.get('itemsPerUnit')) {
                productData.itemsPerUnit = formData.get('itemsPerUnit');
            }
            
            // Simulate API call to add product
            setTimeout(() => {
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                
                // Show success message
                showNotification('Product added successfully!', 'success');
                
                // Reset form
                addProductForm.reset();
                const imagePreview = document.getElementById('imagePreview');
                if (imagePreview) {
                    imagePreview.innerHTML = '';
                }
                
                // Redirect to listings page
                window.location.hash = '#mylisting';
            }, 1500);
        });
    }
});

// Helper function to show notifications
function showNotification(message, type = 'info') {
    // Create notification element if it doesn't exist
    let notificationContainer = document.querySelector('.notification-container');
    
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.className = 'notification-container';
        document.body.appendChild(notificationContainer);
    }
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="close-notification">&times;</button>
    `;
    
    // Add to container
    notificationContainer.appendChild(notification);
    
    // Show with animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
    
    // Close button
    notification.querySelector('.close-notification').addEventListener('click', () => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    });
}

// Format currency in FCFA
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-CM', {
        style: 'currency',
        currency: 'XAF',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}

// Function to initialize urgent sale form
function initializeUrgentSaleForm() {
    // Set minimum date for best before to tomorrow
    const bestBeforeInput = document.getElementById('bestBefore');
    if (bestBeforeInput) {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        bestBeforeInput.min = tomorrow.toISOString().split('T')[0];
    }
    
    // Initialize image preview
    const productImageInput = document.getElementById('productImage');
    const imagePreview = document.getElementById('imagePreview');
    
    if (productImageInput && imagePreview) {
        productImageInput.addEventListener('change', function() {
            imagePreview.innerHTML = '';
            
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'preview-image';
                    imagePreview.appendChild(img);
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
    
    // Initialize price validation
    const originalPriceInput = document.getElementById('originalPrice');
    const reducedPriceInput = document.getElementById('reducedPrice');
    
    if (originalPriceInput && reducedPriceInput) {
        function validatePrices() {
            const originalPrice = parseFloat(originalPriceInput.value);
            const reducedPrice = parseFloat(reducedPriceInput.value);
            
            if (!isNaN(originalPrice) && !isNaN(reducedPrice)) {
                if (reducedPrice >= originalPrice) {
                    reducedPriceInput.setCustomValidity('Reduced price must be lower than original price');
                } else {
                    reducedPriceInput.setCustomValidity('');
                }
            }
        }
        
        originalPriceInput.addEventListener('input', validatePrices);
        reducedPriceInput.addEventListener('input', validatePrices);
    }
    
    // Handle form submission
    const urgentSaleForm = document.getElementById('urgentSaleForm');
    
    if (urgentSaleForm) {
        urgentSaleForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = urgentSaleForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            submitBtn.disabled = true;
            
            // Collect form data
            const formData = new FormData(urgentSaleForm);
            const urgentSaleData = {
                productName: formData.get('productName'),
                originalPrice: formData.get('originalPrice'),
                reducedPrice: formData.get('reducedPrice'),
                quantity: formData.get('quantity'),
                bestBefore: formData.get('bestBefore'),
                reason: formData.get('reason')
            };
            
            // Simulate API call to add urgent sale
            setTimeout(() => {
                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                
                // Show success message
                showNotification('Urgent sale added successfully!', 'success');
                
                // Reset form
                urgentSaleForm.reset();
                const imagePreview = document.getElementById('imagePreview');
                if (imagePreview) {
                    imagePreview.innerHTML = '';
                }
                
                // Redirect to listings page
                window.location.hash = '#mylisting';
            }, 1500);
        });
    }
}

// Function to load REAL dashboard data with actual buyer names and dynamic content
async function loadDashboardData() {
    console.log('Loading REAL dashboard data...');

    // Get dashboard elements
    const quickStats = document.querySelector('.quick-stats');
    const productList = document.querySelector('.product-list');
    const urgentSalesList = document.querySelector('.urgent-sales-list');
    const recentReservations = document.querySelector('.recent-reservations');

    // Show loading indicators
    if (productList) {
        productList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading your products...</div>';
    }

    if (urgentSalesList) {
        urgentSalesList.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading urgent sales...</div>';
    }

    if (recentReservations) {
        recentReservations.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading buyer reservations...</div>';
    }

    try {
        // Load REAL dashboard data from API
        await Promise.all([
            loadRealDashboardStats(),
            loadRealProductListings(),
            loadRealRecentReservations(),
            loadRealUrgentSales()
        ]);

        console.log('✅ Real dashboard data loaded successfully');
    } catch (error) {
        console.error('❌ Error loading dashboard data:', error);
        showDashboardError();
    }
}

// Load REAL dashboard statistics
async function loadRealDashboardStats() {
    try {
        const response = await fetch('/api/farmer/dashboard/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                updateDashboardStats(data.dashboard_data);
            }
        } else {
            // Fallback for file:// protocol
            updateDashboardStats({
                active_listings: 0,
                pending_reservations: 0,
                monthly_revenue: 0,
                unread_notifications: 0,
                total_listings: 0
            });
        }
    } catch (error) {
        console.log('Using fallback stats for file:// protocol');
        updateDashboardStats({
            active_listings: 0,
            pending_reservations: 0,
            monthly_revenue: 0,
            unread_notifications: 0,
            total_listings: 0
        });
    }
}

// Load REAL product listings
async function loadRealProductListings() {
    try {
        const response = await fetch('/api/farmer/listings/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.listings) {
                displayRealProducts(data.listings);
                return;
            }
        }
    } catch (error) {
        console.log('API not available, showing empty state');
    }

    // Show empty state for products
    const productList = document.querySelector('.product-list');
    if (productList) {
        productList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-seedling"></i>
                <h3>No Products Yet</h3>
                <p>Start by adding your first product to the marketplace</p>
                <button onclick="loadContent('addproduct')" class="btn-primary">
                    <i class="fas fa-plus"></i> Add Product
                </button>
            </div>
        `;
    }
}

// Load REAL recent reservations with BUYER NAMES
async function loadRealRecentReservations() {
    try {
        const response = await fetch('/api/farmer/reservations/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.reservations) {
                displayRealReservationsWithBuyerNames(data.reservations);
                return;
            }
        }
    } catch (error) {
        console.log('API not available, showing empty reservations');
    }

    // Show empty state for reservations
    const recentReservations = document.querySelector('.recent-reservations');
    if (recentReservations) {
        recentReservations.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-calendar-check"></i>
                <h3>No Reservations Yet</h3>
                <p>Buyer reservations will appear here when customers book your products</p>
            </div>
        `;
    }
}

// Load REAL urgent sales
async function loadRealUrgentSales() {
    try {
        const response = await fetch('/api/farmer/urgent-sales/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.urgent_sales) {
                displayRealUrgentSales(data.urgent_sales);
                return;
            }
        }
    } catch (error) {
        console.log('API not available, showing empty urgent sales');
    }

    // Show empty state for urgent sales
    const urgentSalesList = document.querySelector('.urgent-sales-list');
    if (urgentSalesList) {
        urgentSalesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-fire"></i>
                <h3>No Urgent Sales</h3>
                <p>Create urgent sales to offer time-limited discounts</p>
                <button onclick="loadContent('addurgentsale')" class="btn-primary">
                    <i class="fas fa-plus"></i> Add Urgent Sale
                </button>
            </div>
        `;
    }
}

// Update dashboard statistics with REAL data
function updateDashboardStats(stats) {
    const quickStats = document.querySelector('.quick-stats');
    if (quickStats) {
        const statItems = quickStats.querySelectorAll('.stat-item .number');
        if (statItems.length >= 3) {
            statItems[0].textContent = stats.active_listings || 0;
            statItems[1].textContent = stats.pending_reservations || 0;
            statItems[2].textContent = formatCurrency(stats.monthly_revenue || 0);
        }
    }
}

// Display REAL products with actual data
function displayRealProducts(products) {
    const productList = document.querySelector('.product-list');
    if (!productList) return;

    if (products.length === 0) {
        productList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-seedling"></i>
                <h3>No Products Yet</h3>
                <p>Start by adding your first product to the marketplace</p>
                <button onclick="loadContent('addproduct')" class="btn-primary">
                    <i class="fas fa-plus"></i> Add Product
                </button>
            </div>
        `;
        return;
    }

    const productsHTML = products.map(product => `
        <div class="product-card" data-product-id="${product.listing_id}">
            <div class="product-image">
                ${product.image_url ?
                    `<img src="${product.image_url}" alt="${product.product_name}">` :
                    `<i class="fas fa-box"></i>`
                }
            </div>
            <div class="product-details">
                <div class="product-name">${escapeHtml(product.product_name)}</div>
                <div class="product-price">${formatCurrency(product.price)}</div>
                <div class="product-quantity">${product.quantity}${product.unit || 'kg'} available</div>
                <div class="product-status">
                    <span class="status-badge ${product.status.toLowerCase()}">${product.status}</span>
                </div>
                <div class="product-actions">
                    <button class="edit-btn" onclick="editProduct(${product.listing_id})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button onclick="viewProductDetails(${product.listing_id})">
                        <i class="fas fa-eye"></i> Details
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    productList.innerHTML = productsHTML;
}

// Display REAL reservations with BUYER NAMES
function displayRealReservationsWithBuyerNames(reservations) {
    const recentReservations = document.querySelector('.recent-reservations');
    if (!recentReservations) return;

    if (reservations.length === 0) {
        recentReservations.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-calendar-check"></i>
                <h3>No Reservations Yet</h3>
                <p>Buyer reservations will appear here when customers book your products</p>
            </div>
        `;
        return;
    }

    const reservationsHTML = reservations.slice(0, 5).map(reservation => `
        <div class="reservation-card" data-reservation-id="${reservation.reservation_id}">
            <div class="reservation-header">
                <div class="buyer-info">
                    <div class="buyer-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="buyer-details">
                        <div class="buyer-name">${escapeHtml(reservation.buyer_name || reservation.buyer_username || 'Unknown Buyer')}</div>
                        <div class="buyer-email">${escapeHtml(reservation.buyer_email || '')}</div>
                    </div>
                </div>
                <div class="reservation-status">
                    <span class="status-badge ${reservation.status.toLowerCase()}">${reservation.status}</span>
                </div>
            </div>
            <div class="reservation-details">
                <div class="product-info">
                    <strong>${escapeHtml(reservation.product_name)}</strong>
                </div>
                <div class="reservation-quantity">
                    Quantity: ${reservation.quantity}${reservation.unit || 'kg'}
                </div>
                <div class="reservation-total">
                    Total: ${formatCurrency(reservation.total_price)}
                </div>
                <div class="reservation-date">
                    ${formatDate(reservation.created_at)}
                </div>
            </div>
            <div class="reservation-actions">
                ${reservation.status === 'Pending' ? `
                    <button class="approve-btn" onclick="approveReservation(${reservation.reservation_id})">
                        <i class="fas fa-check"></i> Approve
                    </button>
                    <button class="reject-btn" onclick="rejectReservation(${reservation.reservation_id})">
                        <i class="fas fa-times"></i> Reject
                    </button>
                ` : `
                    <button onclick="viewReservationDetails(${reservation.reservation_id})">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                `}
            </div>
        </div>
    `).join('');

    recentReservations.innerHTML = `
        <div class="section-header">
            <h3>Recent Reservations</h3>
            <button onclick="loadContent('reservations')" class="view-all-btn">
                <i class="fas fa-arrow-right"></i> View All
            </button>
        </div>
        <div class="reservations-list">
            ${reservationsHTML}
        </div>
    `;
}

// Display REAL urgent sales
function displayRealUrgentSales(urgentSales) {
    const urgentSalesList = document.querySelector('.urgent-sales-list');
    if (!urgentSalesList) return;

    if (urgentSales.length === 0) {
        urgentSalesList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-fire"></i>
                <h3>No Urgent Sales</h3>
                <p>Create urgent sales to offer time-limited discounts</p>
                <button onclick="loadContent('addurgentsale')" class="btn-primary">
                    <i class="fas fa-plus"></i> Add Urgent Sale
                </button>
            </div>
        `;
        return;
    }

    const urgentSalesHTML = urgentSales.map(sale => `
        <div class="urgent-sale-card" data-sale-id="${sale.urgent_sale_id}">
            <div class="urgent-badge">
                <i class="fas fa-fire"></i>
                <span>URGENT</span>
            </div>
            <div class="urgent-details">
                <div class="urgent-name">${escapeHtml(sale.product_name)}</div>
                <div class="urgent-price">
                    <span class="original-price">${formatCurrency(sale.original_price)}</span>
                    <span class="sale-price">${formatCurrency(sale.discounted_price)}</span>
                    <span class="discount">${sale.discount_percentage}% OFF</span>
                </div>
                <div class="urgent-quantity">${sale.quantity}${sale.unit || 'kg'} available</div>
                <div class="urgent-timer">
                    <i class="fas fa-clock"></i>
                    <span>${getTimeRemaining(sale.expires_at)}</span>
                </div>
                <div class="urgent-actions">
                    <button onclick="editUrgentSale(${sale.urgent_sale_id})">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button onclick="viewUrgentSaleDetails(${sale.urgent_sale_id})">
                        <i class="fas fa-eye"></i> Details
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    urgentSalesList.innerHTML = urgentSalesHTML;
}

// Show dashboard error state
function showDashboardError() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Unable to Load Dashboard</h3>
                <p>There was an error loading your dashboard data. Please try again.</p>
                <button onclick="loadDashboardData()" class="btn-primary">
                    <i class="fas fa-refresh"></i> Retry
                </button>
            </div>
        `;
    }
}

// Function to load product listings including urgent sales
function loadProductListings() {
    const regularListings = document.querySelector('.regular-listings');
    const urgentListings = document.querySelector('.urgent-listings');
    
    if (!regularListings && !urgentListings) return;
    
    // Show loading indicators
    if (regularListings) {
        regularListings.innerHTML = '<div class="loading">Loading regular products...</div>';
    }
    
    if (urgentListings) {
        urgentListings.innerHTML = '<div class="loading">Loading urgent sales...</div>';
    }
    
    // Simulate API call to get product listings
    setTimeout(() => {
        // Update regular listings
        if (regularListings) {
            regularListings.innerHTML = `
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-apple-alt"></i>
                    </div>
                    <div class="product-details">
                        <div class="product-name">Fresh Organic Apples</div>
                        <div class="product-price">${formatCurrency(500)}</div>
                        <div class="product-quantity">15kg available</div>
                        <div>
                            <span class="product-tag">organic</span>
                            <span class="product-tag">fresh today</span>
                        </div>
                        <div class="product-actions">
                            <button class="edit-btn">Edit</button>
                            <button>Details</button>
                            <button class="delete-btn">Delete</button>
                        </div>
                    </div>
                </div>
                
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-carrot"></i>
                    </div>
                    <div class="product-details">
                        <div class="product-name">Carrots</div>
                        <div class="product-price">${formatCurrency(1000)}</div>
                        <div class="product-quantity">8kg available</div>
                        <div>
                            <span class="product-tag">fresh today</span>
                        </div>
                        <div class="product-actions">
                            <button class="edit-btn">Edit</button>
                            <button>Details</button>
                            <button class="delete-btn">Delete</button>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Update urgent listings
        if (urgentListings) {
            urgentListings.innerHTML = `
                <div class="urgent-sale-card">
                    <div class="urgent-badge">URGENT</div>
                    <div class="product-image">
                        <i class="fas fa-lemon"></i>
                    </div>
                    <div class="product-details">
                        <div class="product-name">Ripe Lemons</div>
                        <div class="price-container">
                            <div class="original-price">${formatCurrency(800)}</div>
                            <div class="reduced-price">${formatCurrency(500)}</div>
                        </div>
                        <div class="product-quantity">10kg available</div>
                        <div class="expiry-date">Best before: Tomorrow</div>
                        <div class="product-actions">
                            <button class="edit-btn">Edit</button>
                            <button>Details</button>
                            <button class="delete-btn">Delete</button>
                        </div>
                    </div>
                </div>
                
                <div class="urgent-sale-card">
                    <div class="urgent-badge">URGENT</div>
                    <div class="product-image">
                        <i class="fas fa-pepper-hot"></i>
                    </div>
                    <div class="product-details">
                        <div class="product-name">Fresh Peppers</div>
                        <div class="price-container">
                            <div class="original-price">${formatCurrency(1200)}</div>
                            <div class="reduced-price">${formatCurrency(800)}</div>
                        </div>
                        <div class="product-quantity">5kg available</div>
                        <div class="expiry-date">Best before: 2 days</div>
                        <div class="product-actions">
                            <button class="edit-btn">Edit</button>
                            <button>Details</button>
                            <button class="delete-btn">Delete</button>
                        </div>
                    </div>
                </div>
            `;
        }
    }, 1000);
}

// Add this function to load farmer profile data
function loadFarmerProfile() {
    // Get farmer data from localStorage (in a real app, this would come from an API)
    const farmerName = localStorage.getItem('userName') || 'John Farmer';
    const farmerId = localStorage.getItem('userId');
    
    // Update the UI with farmer data
    document.getElementById('farmerName').textContent = farmerName;
    
    // Load profile image if available
    const profileData = JSON.parse(localStorage.getItem('farmerProfileData'));
    if (profileData && profileData.profileImage) {
        document.getElementById('farmerProfileImage').src = profileData.profileImage;
    }
    
    // Set up logout functionality
    document.getElementById('logoutBtn').addEventListener('click', function() {
        // Use the Auth module from auth.js
        if (window.Auth) {
            window.Auth.logout();
        } else {
            // Fallback if Auth module is not available
            localStorage.removeItem('authToken');
            localStorage.removeItem('userType');
            localStorage.removeItem('userName');
            localStorage.removeItem('userId');
            window.location.href = '../index.html';
        }
    });

    // Set up messaging system
    setupMessaging();

    // Set up logout functionality
    setupLogout();

    // Set up dashboard button clicks
    setupDashboardButtons();

    // Set up all navigation links
    setupAllNavigationLinks();
}

// REMOVED: Static content loader - building real dynamic system instead
// ALL STATIC CONTENT REMOVED - Building real dynamic system

// Setup logout functionality
function setupLogout() {
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();

            if (confirm('Are you sure you want to logout?')) {
                // Clear any stored authentication data
                if (typeof Auth !== 'undefined' && Auth.logout) {
                    Auth.logout();
                }

                // Clear local storage
                localStorage.removeItem('farmerToken');
                localStorage.removeItem('farmerData');

                // Redirect to login page
                window.location.href = 'loginfarmer.html';
            }
        });
    }
}

// Setup dashboard button functionality
function setupDashboardButtons() {
    // Add Product button on dashboard
    const addProductBtns = document.querySelectorAll('[href="#addproduct"], .add-product-btn');
    addProductBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            loadContent('addproduct');
        });
    });

    // Add Urgent Sale button on dashboard
    const addUrgentBtns = document.querySelectorAll('[href="#addurgentsale"], .add-urgent-btn');
    addUrgentBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            loadContent('addurgentsale');
        });
    });

    // View All Sales button
    const viewSalesBtns = document.querySelectorAll('.view-sales-btn, .sales-link');
    viewSalesBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            loadContent('saleshistory');
        });
    });

    // Profile button
    const profileBtns = document.querySelectorAll('[href="#profile"], .profile-btn');
    profileBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            loadContent('farmerprofile');
        });
    });
}

// Setup all navigation links
function setupAllNavigationLinks() {
    console.log('Setting up all navigation links...');

    // Get all navigation links
    const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');

    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        const page = href.replace('#', '');

        // Skip logout button (handled separately)
        if (page === '' || link.id === 'logoutBtn') return;

        console.log('Setting up navigation for:', page);

        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Load the content
            loadContent(page);
        });
    });

    // Also setup dashboard action buttons
    setupDashboardActionButtons();
}

// Setup dashboard action buttons specifically
function setupDashboardActionButtons() {
    // This function will be called after content is loaded
    setTimeout(() => {
        // Setup all action buttons with data-page attributes
        const actionBtns = document.querySelectorAll('.action-btn[data-page]');
        actionBtns.forEach(btn => {
            if (!btn.hasAttribute('data-setup')) {
                btn.setAttribute('data-setup', 'true');
                const page = btn.getAttribute('data-page');
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Dashboard button clicked, loading:', page);
                    loadContent(page);
                });
            }
        });

        // Also setup any remaining onclick buttons
        const onclickBtns = document.querySelectorAll('[onclick*="loadContent"]');
        onclickBtns.forEach(btn => {
            if (!btn.hasAttribute('data-setup')) {
                btn.setAttribute('data-setup', 'true');
                // Remove onclick and add proper event listener
                const onclickValue = btn.getAttribute('onclick');
                btn.removeAttribute('onclick');

                // Extract page name from onclick
                const match = onclickValue.match(/loadContent\(['"]([^'"]+)['"]\)/);
                if (match) {
                    const page = match[1];
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('Onclick button clicked, loading:', page);
                        loadContent(page);
                    });
                }
            }
        });
    }, 500);
}

// Setup messaging functionality
function setupMessaging() {
    // Add event listener for message button
    document.addEventListener('click', function(e) {
        if (e.target.id === 'openMessaging' || e.target.closest('#openMessaging')) {
            if (window.messagingSystem) {
                window.messagingSystem.showMessaging();
            }
        }
    });

    // Update message badge count periodically
    updateMessageBadge();
    setInterval(updateMessageBadge, 30000); // Update every 30 seconds
}

// Update message badge count
async function updateMessageBadge() {
    // Skip API calls when running on file:// protocol
    if (window.location.protocol === 'file:') {
        console.log('Skipping message badge update - running on file:// protocol');
        const badge = document.querySelector('.message-badge');
        if (badge) {
            badge.textContent = '2'; // Mock data
            badge.style.display = 'flex';
        }
        return;
    }

    try {
        const response = await fetch('/api/messages/unread-count', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            const badge = document.querySelector('.message-badge');
            if (badge) {
                badge.textContent = data.count;
                badge.style.display = data.count > 0 ? 'flex' : 'none';
            }
        }
    } catch (error) {
        console.error('Error updating message badge:', error);
    }
}

// Setup header functionality
function setupHeader() {
    // Profile dropdown toggle
    const profileDropdownBtn = document.getElementById('profileDropdownBtn');
    const profileDropdown = document.querySelector('.profile-dropdown');

    if (profileDropdownBtn) {
        profileDropdownBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            profileDropdown.classList.toggle('active');
        });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!profileDropdown.contains(e.target)) {
            profileDropdown.classList.remove('active');
        }
    });

    // Header logout button
    const headerLogoutBtn = document.getElementById('headerLogoutBtn');
    if (headerLogoutBtn) {
        headerLogoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }

    // Notification button
    const notificationBtn = document.getElementById('notificationBtn');
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            // Navigate to dedicated notifications page
            window.location.href = 'notifications.html';
        });
    }

    // Update header with user info
    updateHeaderUserInfo();

    // Update page title based on current section
    updatePageTitle();
}

// Update header user information
function updateHeaderUserInfo() {
    const farmerName = Auth.getUserName() || 'John Farmer';
    const profileImage = Auth.getUserAvatar() || '../assets/default-profile.jpg';

    // Update header profile
    const headerFarmerName = document.getElementById('headerFarmerName');
    const headerProfileImage = document.getElementById('headerProfileImage');

    if (headerFarmerName) {
        headerFarmerName.textContent = farmerName;
    }

    if (headerProfileImage) {
        headerProfileImage.src = profileImage;
        headerProfileImage.alt = farmerName;
    }

    // Update sidebar profile as well
    const sidebarFarmerName = document.getElementById('farmerName');
    const sidebarProfileImage = document.getElementById('farmerProfileImage');

    if (sidebarFarmerName) {
        sidebarFarmerName.textContent = farmerName;
    }

    if (sidebarProfileImage) {
        sidebarProfileImage.src = profileImage;
        sidebarProfileImage.alt = farmerName;
    }
}

// Update page title based on current section
function updatePageTitle() {
    const currentPage = getCurrentPage();
    const pageTitle = document.getElementById('pageTitle');
    const lastUpdated = document.getElementById('lastUpdated');

    if (pageTitle) {
        const titles = {
            'dashboard': 'Dashboard',
            'mylisting': 'My Listings',
            'addproduct': 'Add New Product',
            'addurgentsale': 'Add Urgent Sale',
            'editlisting': 'Edit Listings',
            'reservations': 'Reservations',
            'saleshistory': 'Sales History',
            'profile': 'My Profile',
            'settings': 'Settings'
        };

        pageTitle.textContent = titles[currentPage] || 'Dashboard';
    }

    if (lastUpdated) {
        lastUpdated.textContent = `Last updated: ${new Date().toLocaleTimeString()}`;
    }
}

// Get current page from URL hash or active nav item
function getCurrentPage() {
    const hash = window.location.hash.substring(1);
    if (hash) return hash;

    const activeNav = document.querySelector('.nav-menu a.active');
    if (activeNav) {
        return activeNav.getAttribute('href').substring(1);
    }

    return 'dashboard';
}

// Update notification badge
async function updateNotificationBadge() {
    // Skip API calls when running on file:// protocol
    if (window.location.protocol === 'file:') {
        console.log('Skipping notification badge update - running on file:// protocol');
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            badge.textContent = '3'; // Mock data
            badge.style.display = 'flex';
        }
        return;
    }

    try {
        const response = await fetch('/api/notifications/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            const unreadCount = data.notifications ? data.notifications.filter(n => n.status === 'Unread').length : 0;

            const badge = document.querySelector('.notification-badge');
            if (badge) {
                badge.textContent = unreadCount;
                badge.style.display = unreadCount > 0 ? 'flex' : 'none';
            }
        }
    } catch (error) {
        console.error('Error updating notification badge:', error);
    }
}

// Utility functions for dynamic dashboard
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function getTimeRemaining(expiresAt) {
    if (!expiresAt) return 'No expiry';

    const now = new Date();
    const expiry = new Date(expiresAt);
    const diff = expiry - now;

    if (diff <= 0) return 'Expired';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} left`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} left`;
    return 'Less than 1 hour left';
}

// Action functions for dashboard interactions
async function approveReservation(reservationId) {
    try {
        const response = await fetch(`/api/farmer/reservations/${reservationId}/approve/`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            showNotification('Reservation approved successfully!', 'success');
            loadDashboardData(); // Reload dashboard
        } else {
            showNotification('Failed to approve reservation', 'error');
        }
    } catch (error) {
        console.error('Error approving reservation:', error);
        showNotification('Error approving reservation', 'error');
    }
}

async function rejectReservation(reservationId) {
    try {
        const reason = prompt('Please provide a reason for rejection (optional):');

        const response = await fetch(`/api/farmer/reservations/${reservationId}/reject/`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                rejection_reason: reason || 'No reason provided'
            })
        });

        if (response.ok) {
            showNotification('Reservation rejected', 'success');
            loadDashboardData(); // Reload dashboard
        } else {
            showNotification('Failed to reject reservation', 'error');
        }
    } catch (error) {
        console.error('Error rejecting reservation:', error);
        showNotification('Error rejecting reservation', 'error');
    }
}

function viewReservationDetails(reservationId) {
    // Navigate to reservations page with specific reservation
    sessionStorage.setItem('viewReservationId', reservationId);
    loadContent('reservations');
}

function editProduct(productId) {
    // Navigate to edit product page
    sessionStorage.setItem('editProductId', productId);
    loadContent('editlisting');
}

function viewProductDetails(productId) {
    // Navigate to product details
    sessionStorage.setItem('viewProductId', productId);
    loadContent('mylisting');
}

function editUrgentSale(saleId) {
    // Navigate to edit urgent sale
    sessionStorage.setItem('editUrgentSaleId', saleId);
    loadContent('addurgentsale');
}

function viewUrgentSaleDetails(saleId) {
    // Navigate to urgent sale details
    sessionStorage.setItem('viewUrgentSaleId', saleId);
    loadContent('addurgentsale');
}

// Export functions for global access
window.loadContent = loadContent;
window.loadDashboardData = loadDashboardData;
window.approveReservation = approveReservation;
window.rejectReservation = rejectReservation;
window.viewReservationDetails = viewReservationDetails;
window.editProduct = editProduct;
window.viewProductDetails = viewProductDetails;

// ========================================
// FARMER PAGE IMPLEMENTATIONS
// ========================================

function loadMyListingsPage(contentDiv) {
    contentDiv.innerHTML = `
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-seedling"></i> My Product Listings</h1>
                <button onclick="loadContent('addproduct')" class="btn-primary">
                    <i class="fas fa-plus"></i> Add New Product
                </button>
            </div>

            <div class="listings-container">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading your products...
                </div>
            </div>
        </div>
    `;

    // Load real listings data
    setTimeout(() => {
        loadRealProductListings();
    }, 100);
}

function loadAddProductPage(contentDiv) {
    contentDiv.innerHTML = `
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-plus-circle"></i> Add New Product</h1>
                <button onclick="loadContent('mylisting')" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Listings
                </button>
            </div>

            <div class="form-container">
                <form id="addProductForm" class="product-form">
                    <div class="form-group">
                        <label for="productName">Product Name *</label>
                        <input type="text" id="productName" name="product_name" required>
                    </div>

                    <div class="form-group">
                        <label for="productDescription">Description *</label>
                        <textarea id="productDescription" name="description" rows="4" required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="productPrice">Price (CFA) *</label>
                            <input type="number" id="productPrice" name="price" step="0.01" required>
                        </div>

                        <div class="form-group">
                            <label for="productQuantity">Quantity *</label>
                            <input type="number" id="productQuantity" name="quantity" required>
                        </div>

                        <div class="form-group">
                            <label for="productUnit">Unit *</label>
                            <select id="productUnit" name="unit" required>
                                <option value="">Select Unit</option>
                                <option value="kg">Kilogram (kg)</option>
                                <option value="g">Gram (g)</option>
                                <option value="piece">Piece</option>
                                <option value="bunch">Bunch</option>
                                <option value="bag">Bag</option>
                                <option value="liter">Liter</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="productCategory">Category *</label>
                        <select id="productCategory" name="category" required>
                            <option value="">Select Category</option>
                            <option value="vegetables">Vegetables</option>
                            <option value="fruits">Fruits</option>
                            <option value="grains">Grains</option>
                            <option value="dairy">Dairy</option>
                            <option value="meat">Meat</option>
                            <option value="herbs">Herbs & Spices</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="productImage">Product Image</label>
                        <input type="file" id="productImage" name="image" accept="image/*">
                        <div id="imagePreview" class="image-preview"></div>
                    </div>

                    <div class="form-group">
                        <label for="productLocation">Location</label>
                        <input type="text" id="productLocation" name="location" placeholder="Farm location">
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save"></i> Create Product
                        </button>
                        <button type="button" onclick="loadContent('mylisting')" class="btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Initialize form functionality
    setTimeout(() => {
        initializeAddProductForm();
    }, 100);
}

function loadAddUrgentSalePage(contentDiv) {
    contentDiv.innerHTML = `
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-exclamation-triangle"></i> Create Urgent Sale</h1>
                <button onclick="loadContent('dashboard')" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </button>
            </div>

            <div class="form-container">
                <form id="addUrgentSaleForm" class="product-form">
                    <div class="form-group">
                        <label for="urgentProductName">Product Name *</label>
                        <input type="text" id="urgentProductName" name="product_name" required>
                    </div>

                    <div class="form-group">
                        <label for="urgentDescription">Description *</label>
                        <textarea id="urgentDescription" name="description" rows="4" required></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="urgentOriginalPrice">Original Price (CFA) *</label>
                            <input type="number" id="urgentOriginalPrice" name="original_price" step="0.01" required>
                        </div>

                        <div class="form-group">
                            <label for="urgentDiscountPrice">Discounted Price (CFA) *</label>
                            <input type="number" id="urgentDiscountPrice" name="discounted_price" step="0.01" required>
                        </div>

                        <div class="form-group">
                            <label for="urgentQuantity">Quantity *</label>
                            <input type="number" id="urgentQuantity" name="quantity" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="urgentStartDate">Sale Start Date *</label>
                            <input type="datetime-local" id="urgentStartDate" name="start_date" required>
                        </div>

                        <div class="form-group">
                            <label for="urgentEndDate">Sale End Date *</label>
                            <input type="datetime-local" id="urgentEndDate" name="end_date" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="urgentReason">Reason for Urgent Sale</label>
                        <select id="urgentReason" name="reason">
                            <option value="">Select Reason</option>
                            <option value="expiring_soon">Product Expiring Soon</option>
                            <option value="excess_harvest">Excess Harvest</option>
                            <option value="quick_cash">Need Quick Cash</option>
                            <option value="seasonal_end">End of Season</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-fire"></i> Create Urgent Sale
                        </button>
                        <button type="button" onclick="loadContent('dashboard')" class="btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // Initialize form functionality
    setTimeout(() => {
        initializeUrgentSaleForm();
    }, 100);
}

function loadReservationsPage(contentDiv) {
    contentDiv.innerHTML = `
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-calendar-check"></i> Manage Reservations</h1>
                <div class="page-actions">
                    <button onclick="loadContent('dashboard')" class="btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </button>
                </div>
            </div>

            <div class="reservations-filters">
                <div class="filter-group">
                    <label for="statusFilter">Filter by Status:</label>
                    <select id="statusFilter" onchange="filterReservations()">
                        <option value="">All Reservations</option>
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="dateFilter">Filter by Date:</label>
                    <select id="dateFilter" onchange="filterReservations()">
                        <option value="">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                    </select>
                </div>
            </div>

            <div class="reservations-container">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading reservations...
                </div>
            </div>
        </div>
    `;

    // Load real reservations data
    setTimeout(() => {
        loadAllReservations();
    }, 100);
}

function loadSalesHistoryPage(contentDiv) {
    contentDiv.innerHTML = `
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-receipt"></i> Sales History</h1>
                <button onclick="loadContent('dashboard')" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </button>
            </div>

            <div class="sales-summary">
                <div class="summary-card">
                    <div class="summary-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="summary-details">
                        <div class="summary-value" id="totalRevenue">0 CFA</div>
                        <div class="summary-label">Total Revenue</div>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="summary-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="summary-details">
                        <div class="summary-value" id="totalSales">0</div>
                        <div class="summary-label">Total Sales</div>
                    </div>
                </div>

                <div class="summary-card">
                    <div class="summary-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="summary-details">
                        <div class="summary-value" id="averageOrder">0 CFA</div>
                        <div class="summary-label">Average Order</div>
                    </div>
                </div>
            </div>

            <div class="sales-filters">
                <div class="filter-group">
                    <label for="salesDateRange">Date Range:</label>
                    <select id="salesDateRange" onchange="filterSalesHistory()">
                        <option value="30">Last 30 Days</option>
                        <option value="90">Last 3 Months</option>
                        <option value="365">Last Year</option>
                        <option value="all">All Time</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="salesStatus">Status:</label>
                    <select id="salesStatus" onchange="filterSalesHistory()">
                        <option value="">All Transactions</option>
                        <option value="Successful">Successful</option>
                        <option value="Pending">Pending</option>
                        <option value="Failed">Failed</option>
                    </select>
                </div>
            </div>

            <div class="sales-history-container">
                <div class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading sales history...
                </div>
            </div>
        </div>
    `;

    // Load real sales data
    setTimeout(() => {
        loadRealSalesHistory();
    }, 100);
}

function loadProfilePage(contentDiv) {
    contentDiv.innerHTML = `
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-user"></i> My Profile</h1>
                <button onclick="loadContent('dashboard')" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </button>
            </div>

            <div class="profile-container">
                <div class="profile-sidebar">
                    <div class="profile-avatar">
                        <img id="profileAvatar" src="../assets/default-profile.jpg" alt="Profile Picture">
                        <button class="change-avatar-btn" onclick="changeAvatar()">
                            <i class="fas fa-camera"></i>
                        </button>
                        <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                    </div>

                    <div class="profile-info">
                        <h3 id="profileName">Loading...</h3>
                        <p id="profileEmail">Loading...</p>
                        <p id="profileJoinDate">Member since: Loading...</p>
                    </div>
                </div>

                <div class="profile-content">
                    <form id="profileForm" class="profile-form">
                        <div class="form-section">
                            <h3>Personal Information</h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="firstName">First Name *</label>
                                    <input type="text" id="firstName" name="first_name" required>
                                </div>

                                <div class="form-group">
                                    <label for="lastName">Last Name *</label>
                                    <input type="text" id="lastName" name="last_name" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="profileEmailInput">Email *</label>
                                    <input type="email" id="profileEmailInput" name="email" required>
                                </div>

                                <div class="form-group">
                                    <label for="phoneNumber">Phone Number *</label>
                                    <input type="tel" id="phoneNumber" name="phone_number" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h3>Farm Information</h3>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="farmLocation">Farm Location</label>
                                    <input type="text" id="farmLocation" name="location" placeholder="City, Region">
                                </div>

                                <div class="form-group">
                                    <label for="farmSize">Farm Size</label>
                                    <input type="text" id="farmSize" name="farm_size" placeholder="e.g., 5 hectares">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="farmingExperience">Farming Experience</label>
                                <input type="text" id="farmingExperience" name="farming_experience" placeholder="e.g., 10 years">
                            </div>

                            <div class="form-group">
                                <label for="farmDescription">Farm Description</label>
                                <textarea id="farmDescription" name="description" rows="4" placeholder="Tell buyers about your farm..."></textarea>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                            <button type="button" onclick="loadContent('settings')" class="btn-secondary">
                                <i class="fas fa-cog"></i> Account Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // Load real profile data
    setTimeout(() => {
        loadRealProfileData();
    }, 100);
}

function loadSettingsPage(contentDiv) {
    contentDiv.innerHTML = `
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-cog"></i> Account Settings</h1>
                <button onclick="loadContent('profile')" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Profile
                </button>
            </div>

            <div class="settings-container">
                <div class="settings-section">
                    <h3><i class="fas fa-lock"></i> Change Password</h3>
                    <form id="changePasswordForm" class="settings-form">
                        <div class="form-group">
                            <label for="currentPassword">Current Password *</label>
                            <input type="password" id="currentPassword" name="current_password" required>
                        </div>

                        <div class="form-group">
                            <label for="newPassword">New Password *</label>
                            <input type="password" id="newPassword" name="new_password" required>
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">Confirm New Password *</label>
                            <input type="password" id="confirmPassword" name="confirm_password" required>
                        </div>

                        <button type="submit" class="btn-primary">
                            <i class="fas fa-key"></i> Change Password
                        </button>
                    </form>
                </div>

                <div class="settings-section">
                    <h3><i class="fas fa-bell"></i> Notification Preferences</h3>
                    <form id="notificationForm" class="settings-form">
                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="emailNotifications" checked>
                                <span class="checkmark"></span>
                                Email notifications for new reservations
                            </label>
                        </div>

                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="smsNotifications">
                                <span class="checkmark"></span>
                                SMS notifications for urgent matters
                            </label>
                        </div>

                        <div class="checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="marketingEmails">
                                <span class="checkmark"></span>
                                Marketing emails and updates
                            </label>
                        </div>

                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save"></i> Save Preferences
                        </button>
                    </form>
                </div>

                <div class="settings-section">
                    <h3><i class="fas fa-shield-alt"></i> Account Security</h3>
                    <div class="security-info">
                        <div class="security-item">
                            <div class="security-icon">
                                <i class="fas fa-check-circle text-success"></i>
                            </div>
                            <div class="security-details">
                                <h4>Account Verified</h4>
                                <p>Your account has been verified by admin</p>
                            </div>
                        </div>

                        <div class="security-item">
                            <div class="security-icon">
                                <i class="fas fa-clock text-warning"></i>
                            </div>
                            <div class="security-details">
                                <h4>Last Login</h4>
                                <p id="lastLoginTime">Loading...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section danger-zone">
                    <h3><i class="fas fa-exclamation-triangle"></i> Danger Zone</h3>
                    <div class="danger-actions">
                        <button onclick="deactivateAccount()" class="btn-danger">
                            <i class="fas fa-user-times"></i> Deactivate Account
                        </button>
                        <p class="danger-warning">
                            Deactivating your account will hide your listings from buyers. You can reactivate anytime.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Initialize settings functionality
    setTimeout(() => {
        initializeSettingsPage();
    }, 100);
}

function loadEditListingPage(contentDiv) {
    contentDiv.innerHTML = `
        <div class="main-content">
            <div class="page-header">
                <h1><i class="fas fa-edit"></i> Edit Product Listings</h1>
                <button onclick="loadContent('mylisting')" class="btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Listings
                </button>
            </div>

            <div class="edit-listings-container">
                <div class="search-bar">
                    <input type="text" id="searchListings" placeholder="Search your products..." onkeyup="searchListings()">
                    <i class="fas fa-search"></i>
                </div>

                <div class="listings-grid">
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i> Loading your products for editing...
                    </div>
                </div>
            </div>
        </div>
    `;

    // Load editable listings
    setTimeout(() => {
        loadEditableListings();
    }, 100);
}

// ========================================
// SUPPORTING FUNCTIONS FOR FARMER PAGES
// ========================================

function initializeAddProductForm() {
    const form = document.getElementById('addProductForm');
    if (!form) return;

    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = new FormData(form);
        const productData = Object.fromEntries(formData.entries());

        try {
            const response = await fetch('/api/farmer/listings/', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${Auth.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(productData)
            });

            const result = await response.json();

            if (result.success) {
                showNotification('Product created successfully!', 'success');
                loadContent('mylisting');
            } else {
                showNotification('Failed to create product: ' + (result.message || 'Unknown error'), 'error');
            }
        } catch (error) {
            console.error('Error creating product:', error);
            showNotification('Network error. Please try again.', 'error');
        }
    });

    // Initialize image preview
    initializeImagePreview();
}

function initializeUrgentSaleForm() {
    const form = document.getElementById('addUrgentSaleForm');
    if (!form) return;

    form.addEventListener('submit', async (e) => {
        e.preventDefault();

        const formData = new FormData(form);
        const saleData = Object.fromEntries(formData.entries());

        try {
            const response = await fetch('/api/farmer/urgent-sales/', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${Auth.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(saleData)
            });

            const result = await response.json();

            if (result.success) {
                showNotification('Urgent sale created successfully!', 'success');
                loadContent('dashboard');
            } else {
                showNotification('Failed to create urgent sale: ' + (result.message || 'Unknown error'), 'error');
            }
        } catch (error) {
            console.error('Error creating urgent sale:', error);
            showNotification('Network error. Please try again.', 'error');
        }
    });
}

async function loadAllReservations() {
    try {
        const response = await fetch('/api/farmer/reservations/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            displayAllReservations(result.reservations);
        } else {
            showNotification('Failed to load reservations: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error loading reservations:', error);
        showNotification('Network error loading reservations', 'error');
    }
}

function displayAllReservations(reservations) {
    const container = document.querySelector('.reservations-container');
    if (!container) return;

    if (reservations.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-calendar-check"></i>
                <h3>No Reservations Yet</h3>
                <p>When buyers reserve your products, they'll appear here</p>
            </div>
        `;
        return;
    }

    const reservationsHTML = reservations.map(reservation => `
        <div class="reservation-card ${reservation.status.toLowerCase()}">
            <div class="reservation-header">
                <div class="reservation-info">
                    <h4>${reservation.listing?.product_name || 'Product'}</h4>
                    <p class="buyer-name">
                        <i class="fas fa-user"></i>
                        ${reservation.buyer?.first_name || ''} ${reservation.buyer?.last_name || ''}
                    </p>
                </div>
                <div class="reservation-status">
                    <span class="status-badge ${reservation.status.toLowerCase()}">${reservation.status}</span>
                </div>
            </div>

            <div class="reservation-details">
                <div class="detail-item">
                    <span class="label">Quantity:</span>
                    <span class="value">${reservation.quantity}</span>
                </div>
                <div class="detail-item">
                    <span class="label">Total Amount:</span>
                    <span class="value">${(reservation.listing?.price * reservation.quantity).toLocaleString()} CFA</span>
                </div>
                <div class="detail-item">
                    <span class="label">Date:</span>
                    <span class="value">${new Date(reservation.created_at).toLocaleDateString()}</span>
                </div>
            </div>

            ${reservation.status === 'Pending' ? `
                <div class="reservation-actions">
                    <button onclick="approveReservation(${reservation.reservation_id})" class="btn-success">
                        <i class="fas fa-check"></i> Approve
                    </button>
                    <button onclick="rejectReservation(${reservation.reservation_id})" class="btn-danger">
                        <i class="fas fa-times"></i> Reject
                    </button>
                </div>
            ` : ''}
        </div>
    `).join('');

    container.innerHTML = reservationsHTML;
}

async function loadRealSalesHistory() {
    try {
        const response = await fetch('/api/farmer/sales-history/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            displaySalesHistory(result.sales_history);
        } else {
            showNotification('Failed to load sales history: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error loading sales history:', error);
        showNotification('Network error loading sales history', 'error');
    }
}

function displaySalesHistory(salesData) {
    const container = document.querySelector('.sales-history-container');
    if (!container) return;

    // Update summary cards
    const totalRevenue = salesData.transactions.reduce((sum, t) => sum + t.total_amount, 0);
    const totalSales = salesData.transactions.length;
    const averageOrder = totalSales > 0 ? totalRevenue / totalSales : 0;

    document.getElementById('totalRevenue').textContent = totalRevenue.toLocaleString() + ' CFA';
    document.getElementById('totalSales').textContent = totalSales;
    document.getElementById('averageOrder').textContent = averageOrder.toLocaleString() + ' CFA';

    if (salesData.transactions.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-receipt"></i>
                <h3>No Sales Yet</h3>
                <p>Your completed sales will appear here</p>
            </div>
        `;
        return;
    }

    const salesHTML = salesData.transactions.map(transaction => `
        <div class="sale-card">
            <div class="sale-header">
                <div class="sale-info">
                    <h4>${transaction.product_name}</h4>
                    <p class="buyer-info">
                        <i class="fas fa-user"></i> ${transaction.buyer_name}
                    </p>
                </div>
                <div class="sale-amount">
                    <span class="amount">${transaction.total_amount.toLocaleString()} CFA</span>
                    <span class="status-badge ${transaction.status.toLowerCase()}">${transaction.status}</span>
                </div>
            </div>

            <div class="sale-details">
                <div class="detail-row">
                    <span class="label">Quantity:</span>
                    <span class="value">${transaction.quantity}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Unit Price:</span>
                    <span class="value">${transaction.unit_price.toLocaleString()} CFA</span>
                </div>
                <div class="detail-row">
                    <span class="label">Date:</span>
                    <span class="value">${new Date(transaction.created_at).toLocaleDateString()}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Transaction ID:</span>
                    <span class="value">${transaction.transaction_id}</span>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = `
        <div class="sales-list">
            ${salesHTML}
        </div>

        <div class="pagination">
            <button onclick="loadSalesPage(${salesData.pagination.current_page - 1})"
                    ${!salesData.pagination.has_previous ? 'disabled' : ''}>
                <i class="fas fa-chevron-left"></i> Previous
            </button>

            <span class="page-info">
                Page ${salesData.pagination.current_page} of ${salesData.pagination.total_pages}
            </span>

            <button onclick="loadSalesPage(${salesData.pagination.current_page + 1})"
                    ${!salesData.pagination.has_next ? 'disabled' : ''}>
                Next <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    `;
}

async function loadRealProfileData() {
    try {
        const response = await fetch('/api/farmer/profile/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            populateProfileForm(result.profile);
        } else {
            showNotification('Failed to load profile: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error loading profile:', error);
        showNotification('Network error loading profile', 'error');
    }
}

function populateProfileForm(profile) {
    // Update profile display
    document.getElementById('profileName').textContent = `${profile.farmer?.first_name || ''} ${profile.farmer?.last_name || ''}`;
    document.getElementById('profileEmail').textContent = profile.farmer?.email || '';

    // Populate form fields
    document.getElementById('firstName').value = profile.farmer?.first_name || '';
    document.getElementById('lastName').value = profile.farmer?.last_name || '';
    document.getElementById('profileEmailInput').value = profile.farmer?.email || '';
    document.getElementById('phoneNumber').value = profile.farmer?.phone_number || '';
    document.getElementById('farmLocation').value = profile.location || '';
    document.getElementById('farmSize').value = profile.farm_size || '';
    document.getElementById('farmingExperience').value = profile.farming_experience || '';
    document.getElementById('farmDescription').value = profile.description || '';

    // Initialize profile form submission
    const form = document.getElementById('profileForm');
    if (form) {
        form.addEventListener('submit', updateProfile);
    }
}

async function updateProfile(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const profileData = Object.fromEntries(formData.entries());

    try {
        const response = await fetch('/api/farmer/profile/update/', {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(profileData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Profile updated successfully!', 'success');
        } else {
            showNotification('Failed to update profile: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error updating profile:', error);
        showNotification('Network error updating profile', 'error');
    }
}

function initializeSettingsPage() {
    // Initialize change password form
    const passwordForm = document.getElementById('changePasswordForm');
    if (passwordForm) {
        passwordForm.addEventListener('submit', changePassword);
    }

    // Initialize notification form
    const notificationForm = document.getElementById('notificationForm');
    if (notificationForm) {
        notificationForm.addEventListener('submit', updateNotificationPreferences);
    }
}

async function changePassword(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const passwordData = Object.fromEntries(formData.entries());

    if (passwordData.new_password !== passwordData.confirm_password) {
        showNotification('New passwords do not match', 'error');
        return;
    }

    try {
        const response = await fetch('/api/farmer/change-password/', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(passwordData)
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Password changed successfully!', 'success');
            e.target.reset();
        } else {
            showNotification('Failed to change password: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error changing password:', error);
        showNotification('Network error changing password', 'error');
    }
}

async function updateNotificationPreferences(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const preferences = {
        email_notifications: formData.get('emailNotifications') === 'on',
        sms_notifications: formData.get('smsNotifications') === 'on',
        marketing_emails: formData.get('marketingEmails') === 'on'
    };

    // For now, just show success (implement API endpoint later)
    showNotification('Notification preferences updated!', 'success');
}

async function loadEditableListings() {
    try {
        const response = await fetch('/api/farmer/listings/', {
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            displayEditableListings(result.listings);
        } else {
            showNotification('Failed to load listings: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error loading listings:', error);
        showNotification('Network error loading listings', 'error');
    }
}

function displayEditableListings(listings) {
    const container = document.querySelector('.listings-grid');
    if (!container) return;

    if (listings.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-seedling"></i>
                <h3>No Products to Edit</h3>
                <p>Create some products first to edit them</p>
                <button onclick="loadContent('addproduct')" class="btn-primary">
                    <i class="fas fa-plus"></i> Add Product
                </button>
            </div>
        `;
        return;
    }

    const listingsHTML = listings.map(listing => `
        <div class="listing-edit-card">
            <div class="listing-image">
                <img src="${listing.image_url || '../assets/default-product.jpg'}" alt="${listing.product_name}">
            </div>

            <div class="listing-info">
                <h4>${listing.product_name}</h4>
                <p class="listing-price">${listing.price.toLocaleString()} CFA</p>
                <p class="listing-quantity">Quantity: ${listing.quantity}</p>
                <span class="listing-status ${listing.status.toLowerCase()}">${listing.status}</span>
            </div>

            <div class="listing-actions">
                <button onclick="editListingDetails(${listing.listing_id})" class="btn-primary">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button onclick="deleteListing(${listing.listing_id})" class="btn-danger">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = listingsHTML;
}

async function editListingDetails(listingId) {
    // For now, show a simple prompt (implement full edit form later)
    const newPrice = prompt('Enter new price:');
    if (newPrice && !isNaN(newPrice)) {
        try {
            const response = await fetch(`/api/farmer/listings/${listingId}/update/`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${Auth.getToken()}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ price: parseFloat(newPrice) })
            });

            const result = await response.json();

            if (result.success) {
                showNotification('Listing updated successfully!', 'success');
                loadEditableListings(); // Reload listings
            } else {
                showNotification('Failed to update listing: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Error updating listing:', error);
            showNotification('Network error updating listing', 'error');
        }
    }
}

async function deleteListing(listingId) {
    if (!confirm('Are you sure you want to delete this listing?')) {
        return;
    }

    try {
        const response = await fetch(`/api/farmer/listings/${listingId}/delete/`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${Auth.getToken()}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            showNotification('Listing deleted successfully!', 'success');
            loadEditableListings(); // Reload listings
        } else {
            showNotification('Failed to delete listing: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error deleting listing:', error);
        showNotification('Network error deleting listing', 'error');
    }
}

// Utility functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function changeAvatar() {
    document.getElementById('avatarInput').click();
}

function filterReservations() {
    // Implement reservation filtering
    loadAllReservations();
}

function filterSalesHistory() {
    // Implement sales history filtering
    loadRealSalesHistory();
}

function searchListings() {
    // Implement listing search
    loadEditableListings();
}

function deactivateAccount() {
    if (confirm('Are you sure you want to deactivate your account? This will hide your listings from buyers.')) {
        showNotification('Account deactivation feature coming soon', 'info');
    }
}
window.editUrgentSale = editUrgentSale;
window.viewUrgentSaleDetails = viewUrgentSaleDetails;
