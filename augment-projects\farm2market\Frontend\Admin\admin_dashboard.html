<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - AGRIPORT</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="admin_dashboard_styles.css">


</head>
<body>
    <!-- Admin Header -->
    <div class="admin-header">
        <div class="logo">
            <i class="fas fa-seedling"></i>
            <h1>AGRIPORT Admin</h1>
        </div>
        <div class="admin-info">
            <span class="admin-name" id="adminName">Loading...</span>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>
    </div>

    <!-- Admin Container -->
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="nav-item active" onclick="showSection('dashboard')">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </div>
            <div class="nav-item" onclick="showSection('users')">
                <i class="fas fa-users"></i> Manage Users
            </div>
            <div class="nav-item" onclick="showSection('farmers')">
                <i class="fas fa-user-check"></i> Pending Farmers
            </div>
            <div class="nav-item" onclick="showSection('admins')">
                <i class="fas fa-user-shield"></i> Admin Management
            </div>
            <div class="nav-item" onclick="showSection('transactions')">
                <i class="fas fa-credit-card"></i> Transactions
            </div>
            <div class="nav-item" onclick="showSection('notifications')">
                <i class="fas fa-bell"></i> Notifications
            </div>
            <div class="nav-item" onclick="showSection('broadcast')">
                <i class="fas fa-bullhorn"></i> Broadcast
            </div>
            <div class="nav-item" onclick="showSection('search')">
                <i class="fas fa-search"></i> Global Search
            </div>
            <div class="nav-item" onclick="showSection('analytics')">
                <i class="fas fa-chart-bar"></i> Analytics
            </div>
            <div class="nav-item" onclick="showSection('roles')">
                <i class="fas fa-user-cog"></i> Role Management
            </div>
            <div class="nav-item" onclick="showSection('settings')">
                <i class="fas fa-cog"></i> System Settings
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="content-section active">
                <div class="page-header">
                    <h2>Dashboard Overview</h2>
                    <p>Welcome to the Agriport administration panel</p>
                </div>

                <div class="stats-grid" id="statsGrid">
                    <div class="stat-card users">
                        <div class="stat-icon"><i class="fas fa-users"></i></div>
                        <div class="stat-number" id="totalUsers">-</div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    <div class="stat-card farmers">
                        <div class="stat-icon"><i class="fas fa-tractor"></i></div>
                        <div class="stat-number" id="totalFarmers">-</div>
                        <div class="stat-label">Farmers</div>
                    </div>
                    <div class="stat-card buyers">
                        <div class="stat-icon"><i class="fas fa-shopping-cart"></i></div>
                        <div class="stat-number" id="totalBuyers">-</div>
                        <div class="stat-label">Buyers</div>
                    </div>
                    <div class="stat-card pending">
                        <div class="stat-icon"><i class="fas fa-clock"></i></div>
                        <div class="stat-number" id="pendingFarmers">-</div>
                        <div class="stat-label">Pending Approvals</div>
                    </div>
                </div>

                <div class="content-section active">
                    <div class="section-header">
                        <h3>Recent Activity</h3>
                    </div>
                    <div class="section-content">
                        <div id="recentActivity">
                            <div class="loading">
                                <i class="fas fa-spinner"></i>
                                <p>Loading recent activity...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users Management Section -->
            <div id="users" class="content-section">
                <div class="section-header">
                    <h3>User Management</h3>
                    <div>
                        <select id="userTypeFilter" onchange="filterUsers()">
                            <option value="">All Users</option>
                            <option value="Farmer">Farmers</option>
                            <option value="Buyer">Buyers</option>
                            <option value="Admin">Admins</option>
                        </select>
                        <input type="text" id="userSearch" placeholder="Search users..." onkeyup="searchUsers()">
                    </div>
                </div>
                <div class="section-content">
                    <div id="usersTable">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading users...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Farmers Section -->
            <div id="farmers" class="content-section">
                <div class="section-header">
                    <h3>Pending Farmer Approvals</h3>
                </div>
                <div class="section-content">
                    <div id="pendingFarmersTable">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading pending farmers...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admin Management Section -->
            <div id="admins" class="content-section">
                <div class="section-header">
                    <h3>Admin Management</h3>
                    <button class="btn btn-primary" onclick="showCreateAdminModal()">
                        <i class="fas fa-plus"></i> Create New Admin
                    </button>
                </div>
                <div class="section-content">
                    <div id="adminsTable">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading admins...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transactions Section -->
            <div id="transactions" class="content-section">
                <div class="section-header">
                    <h3>Transaction Management</h3>
                    <div>
                        <select id="transactionStatusFilter" onchange="filterTransactions()">
                            <option value="">All Transactions</option>
                            <option value="Pending">Pending</option>
                            <option value="Successful">Successful</option>
                            <option value="Failed">Failed</option>
                        </select>
                        <input type="text" id="transactionSearch" placeholder="Search transactions..." onkeyup="searchTransactions()">
                    </div>
                </div>
                <div class="section-content">
                    <div id="transactionsTable">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading transactions...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Section -->
            <div id="notifications" class="content-section">
                <div class="section-header">
                    <h3>Notification Center</h3>
                    <button class="btn btn-primary" onclick="markAllNotificationsRead()">
                        <i class="fas fa-check-double"></i> Mark All Read
                    </button>
                </div>
                <div class="section-content">
                    <div id="notificationsTable">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading notifications...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Broadcast Section -->
            <div id="broadcast" class="content-section">
                <div class="section-header">
                    <h3>Broadcast System</h3>
                    <button class="btn btn-primary" onclick="showBroadcastModal()">
                        <i class="fas fa-bullhorn"></i> Send Broadcast
                    </button>
                </div>
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-paper-plane"></i></div>
                            <div class="stat-number" id="totalBroadcasts">-</div>
                            <div class="stat-label">Total Broadcasts</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon"><i class="fas fa-users"></i></div>
                            <div class="stat-number" id="totalRecipients">-</div>
                            <div class="stat-label">Total Recipients</div>
                        </div>
                    </div>
                    <div id="broadcastHistory">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading broadcast history...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Global Search Section -->
            <div id="search" class="content-section">
                <div class="section-header">
                    <h3>Global Search</h3>
                </div>
                <div class="section-content">
                    <div class="form-group">
                        <input type="text" id="globalSearchInput" placeholder="Search across all system data..." onkeyup="performGlobalSearch()">
                    </div>
                    <div class="form-group">
                        <select id="searchTypeFilter">
                            <option value="all">All Data</option>
                            <option value="users">Users Only</option>
                            <option value="transactions">Transactions Only</option>
                            <option value="listings">Listings Only</option>
                            <option value="reservations">Reservations Only</option>
                        </select>
                    </div>
                    <div id="searchResults">
                        <p>Enter a search term to find data across the system...</p>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics" class="content-section">
                <div class="section-header">
                    <h3>Enhanced Analytics</h3>
                    <button class="btn btn-primary" onclick="refreshAnalytics()">
                        <i class="fas fa-sync"></i> Refresh Data
                    </button>
                </div>
                <div class="section-content">
                    <div id="analyticsContent">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading analytics...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role Management Section -->
            <div id="roles" class="content-section">
                <div class="section-header">
                    <h3>Role Management</h3>
                    <button class="btn btn-primary" onclick="showAssignRoleModal()">
                        <i class="fas fa-user-plus"></i> Assign Role
                    </button>
                </div>
                <div class="section-content">
                    <div id="rolesContent">
                        <div class="loading">
                            <i class="fas fa-spinner"></i>
                            <p>Loading roles...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings" class="content-section">
                <div class="section-header">
                    <h3>System Settings</h3>
                </div>
                <div class="section-content">
                    <div class="form-group">
                        <label>Email Service Status</label>
                        <div id="emailStatus" class="badge">Checking...</div>
                    </div>
                    <div class="form-group">
                        <label>Database Status</label>
                        <div id="databaseStatus" class="badge">Checking...</div>
                    </div>
                    <div class="form-group">
                        <label>System Health</label>
                        <div id="systemHealth" class="badge">Checking...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Admin Modal -->
    <div id="createAdminModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Create New Admin</h3>
                <span class="close" onclick="closeCreateAdminModal()">&times;</span>
            </div>
            <form id="createAdminForm">
                <div id="createAdminAlert"></div>
                <div class="form-group">
                    <label for="adminUsername">Username</label>
                    <input type="text" id="adminUsername" required>
                </div>
                <div class="form-group">
                    <label for="adminEmail">Email</label>
                    <input type="email" id="adminEmail" required>
                </div>
                <div class="form-group">
                    <label for="adminFirstName">First Name</label>
                    <input type="text" id="adminFirstName" required>
                </div>
                <div class="form-group">
                    <label for="adminLastName">Last Name</label>
                    <input type="text" id="adminLastName" required>
                </div>
                <div class="form-group">
                    <label for="adminPassword">Password</label>
                    <input type="password" id="adminPassword" required>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="adminSuperuser"> Grant Superuser Privileges
                    </label>
                </div>
                <button type="submit" class="btn btn-primary">Create Admin</button>
            </form>
        </div>
    </div>

    <!-- Broadcast Modal -->
    <div id="broadcastModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Send Broadcast Message</h3>
                <span class="close" onclick="closeBroadcastModal()">&times;</span>
            </div>
            <form id="broadcastForm">
                <div id="broadcastAlert"></div>
                <div class="form-group">
                    <label for="broadcastTitle">Title</label>
                    <input type="text" id="broadcastTitle" required>
                </div>
                <div class="form-group">
                    <label for="broadcastMessage">Message</label>
                    <textarea id="broadcastMessage" rows="4" style="width: 100%; padding: 0.75rem; border: 1px solid #ddd; border-radius: 5px;" required></textarea>
                </div>
                <div class="form-group">
                    <label for="broadcastTarget">Target Group</label>
                    <select id="broadcastTarget" required>
                        <option value="all">All Users</option>
                        <option value="farmers">Farmers Only</option>
                        <option value="buyers">Buyers Only</option>
                        <option value="admins">Admins Only</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="broadcastEmail"> Also send via email
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="broadcastUrgent"> Mark as urgent
                    </label>
                </div>
                <button type="submit" class="btn btn-primary">Send Broadcast</button>
            </form>
        </div>
    </div>

    <!-- Assign Role Modal -->
    <div id="assignRoleModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Assign Role to Admin</h3>
                <span class="close" onclick="closeAssignRoleModal()">&times;</span>
            </div>
            <form id="assignRoleForm">
                <div id="assignRoleAlert"></div>
                <div class="form-group">
                    <label for="roleAdminSelect">Select Admin</label>
                    <select id="roleAdminSelect" required>
                        <option value="">Loading admins...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="roleSelect">Select Role</label>
                    <select id="roleSelect" required>
                        <option value="">Loading roles...</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Assign Role</button>
            </form>
        </div>
    </div>

    <!-- User Details Modal -->
    <div id="userDetailsModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3>User Details</h3>
                <span class="close" onclick="closeUserDetailsModal()">&times;</span>
            </div>
            <div id="userDetailsContent">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>Loading user details...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="admin_dashboard.js"></script>
</body>
</html>
