 :root {
            --primary-green: #4CAF50;
            --dark-green: #388E3C;
            --light-green: #C8E6C9;
            --earth-brown: #8D6E63;
            --light-beige: #FFF8E1;
            --text-dark: #333;
            --text-medium: #555;
            --text-light: #777;
            --white: #fff;
            --gray-bg: #f5f5f5;
            --sidebar-width: 250px;
            --sidebar-collapsed: 100px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--gray-bg);
            color: var(--text-dark);
            transition: margin-left 0.3s;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: var(--sidebar-width) 1fr;
            height: 100%;
            width: 100%;
            align-content: start;
        }
        
        /* Enhanced Sidebar Styles */
        .sidebar {
            background-color: var(--white);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            position: fixed;
            height: 100vh;
            width: var(--sidebar-width);
            transition: width 0.3s;
            z-index: 100;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            text-align: center;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
        }

        .logo-icon {
            color: var(--primary-green);
            font-size: 24px;
            margin-right: 10px;
        }

        .sidebar-header h1 {
            color: var(--primary-green);
            font-size: 22px;
            font-weight: bold;
        }

        .sidebar-header h3 {
            color: var(--text-medium);
            font-size: 14px;
            margin-bottom: 15px;
        }

        .farmer-info {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .farmer-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid var(--light-green);
            margin-bottom: 8px;
        }

        .farmer-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .trust-badge {
            background-color: var(--primary-green);
            color: white;
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 12px;
            display: inline-flex;
            align-items: center;
        }

        .trust-badge i {
            margin-left: 4px;
        }

        .sidebar-search {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .search-container {
            position: relative;
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 20px;
            padding: 8px 15px;
        }

        .search-container i {
            color: var(--text-light);
            margin-right: 8px;
        }

        .search-container input {
            background: none;
            border: none;
            outline: none;
            width: 100%;
            font-size: 14px;
        }

        .nav-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            overflow-y: auto;
            flex-grow: 1;
        }

        .nav-section {
            margin-top: 15px;
        }

        .section-title {
            display: block;
            padding: 0 20px;
            margin-bottom: 8px;
            font-size: 12px;
            text-transform: uppercase;
            color: var(--text-light);
            font-weight: 600;
        }

        .nav-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-medium);
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .nav-menu a:hover, .nav-menu a.active {
            background-color: var(--light-green);
            color: var(--primary-green);
            border-left: 3px solid var(--primary-green);
        }

        .nav-menu a.active {
            font-weight: 600;
        }

        .nav-menu a i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
            font-size: 16px;
        }

        .sidebar-footer {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            margin-top: auto;
        }

        .online-status {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-dot.online {
            background-color: var(--primary-green);
        }

        .status-text {
            font-size: 13px;
            color: var(--text-medium);
        }

        .logout-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f5f5;
            color: var(--text-medium);
            padding: 10px;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.2s;
        }

        .logout-btn:hover {
            background-color: #ffebee;
            color: #f44336;
        }

        .logout-btn i {
            margin-right: 8px;
        }

        /* Collapsed sidebar styles */
        .sidebar.collapsed {
            width: var(--sidebar-collapsed);
        }

        .sidebar.collapsed .section-title,
        .sidebar.collapsed .farmer-name,
        .sidebar.collapsed .sidebar-search,
        .sidebar.collapsed .status-text {
            display: none;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 15px 0;
        }

        .sidebar.collapsed .farmer-avatar {
            width: 40px;
            height: 40px;
        }

        .sidebar.collapsed .nav-menu a span:not(.tooltip) {
            display: none;
        }

        .sidebar.collapsed .nav-menu a {
            justify-content: center;
            padding: 15px 0;
        }

        .sidebar.collapsed .nav-menu a i {
            margin-right: 0;
            font-size: 18px;
        }

        .sidebar.collapsed .logout-btn span {
            display: none;
        }

        .sidebar.collapsed .logout-btn {
            justify-content: center;
        }

        .sidebar.collapsed .logout-btn i {
            margin-right: 0;
        }
        
        .sidebar:hover {
            width: var(--sidebar-width);
        }
        
        .sidebar.collapsed {
            width: var(--sidebar-collapsed);
        }
        
        .sidebar-header {
            text-align: center;
            padding: 0 20px 20px;
            border-bottom: 1px solid #eee;
            white-space: nowrap;
        }
        
        .sidebar-header img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 10px;
            border: 3px solid var(--primary-green);
        }
        
        .sidebar-header h3 {
            color: var(--primary-green);
            font-size: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .sidebar-header .trust-badge {
            display: inline-block;
            background-color: var(--primary-green);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            margin-top: 5px;
            white-space: nowrap;
        }
        
        .nav-menu {
            list-style: none;
            margin-top: 20px;
        }
        
        .nav-menu li {
            margin-bottom: 5px;
            position: relative;
        }
        
        .nav-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-medium);
            text-decoration: none;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .nav-menu a:hover, .nav-menu a.active {
            background-color: var(--light-green);
            color: var(--primary-green);
        }
        
        .nav-menu a i {
            margin-right: 15px;
            width: 20px;
            text-align: center;
            font-size: 18px;
        }
        
        .nav-menu .tooltip {
            position: absolute;
            left: calc(var(--sidebar-collapsed) + 10px);
            top: 50%;
            transform: translateY(-50%);
            background-color: var(--dark-green);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s;
            z-index: 1000;
            pointer-events: none;
            white-space: nowrap;
        }
        
        .sidebar.collapsed .nav-menu a:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }
        
        .toggle-sidebar {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: var(--text-medium);
            cursor: pointer;
            font-size: 16px;
        }
        
        /* Main Content Area */
        .main-content {
            height: 100%;
            width: 100%;
            padding: 20px;
            margin-left: 20px;
            gap: 15px;
        }
        
        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed);
        }

        /* Dashboard Header */
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--white);
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border-left: 4px solid var(--primary-green);
        }

        .header-left h2 {
            color: var(--text-dark);
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .last-updated {
            color: var(--text-light);
            font-size: 12px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header-btn {
            position: relative;
            background: none;
            border: none;
            padding: 10px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-medium);
        }

        .header-btn:hover {
            background: var(--light-green);
            color: var(--primary-green);
        }

        .header-btn i {
            font-size: 18px;
        }

        /* Notification and Message Badges */
        .notification-badge,
        .message-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: none;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .notification-badge.show,
        .message-badge.show {
            display: flex;
        }

        /* Profile Dropdown */
        .profile-dropdown {
            position: relative;
        }

        .profile-btn {
            background: var(--light-beige);
            border: 1px solid var(--primary-green);
            padding: 8px 12px;
            border-radius: 25px;
        }

        .profile-btn img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            object-fit: cover;
        }

        .profile-btn span {
            font-weight: 500;
            color: var(--text-dark);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--white);
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            min-width: 180px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .profile-dropdown.active .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 16px;
            color: var(--text-dark);
            text-decoration: none;
            transition: background 0.3s ease;
        }

        .dropdown-item:hover {
            background: var(--light-green);
            color: var(--primary-green);
        }

        .dropdown-divider {
            height: 1px;
            background: #eee;
            margin: 5px 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background-color: var(--white);
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .header h1 {
            color: var(--primary-green);
            font-size: 24px;
        }
        
        .quick-stats {
            display: flex;
            gap: 15px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px 15px;
            background-color: var(--light-green);
            border-radius: 8px;
            min-width: 100px;
        }
        
        .stat-item .number {
            font-size: 20px;
            font-weight: bold;
            color: var(--dark-green);
        }
        
        .stat-item .label {
            font-size: 12px;
            color: var(--text-medium);
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .message-btn {
            position: relative;
            background: #f8f9fa;
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .message-btn:hover {
            background: #e9ecef;
            transform: scale(1.05);
        }

        .message-btn i {
            font-size: 18px;
            color: #2c5530;
        }

        .message-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            border: 2px solid white;
        }

        .notification-bell {
            position: relative;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-bell:hover {
            background: #e9ecef;
            transform: scale(1.05);
        }

        .notification-bell i {
            font-size: 18px;
            color: #2c5530;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            border: 2px solid white;
        }
        
        /* Dashboard Sections */
        .dashboard-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .section {
            background-color: var(--white);
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .section-header h2 {
            font-size: 18px;
            color: var(--primary-green);
        }
        
        .section-header .action {
            color: var(--primary-green);
            font-size: 14px;
            text-decoration: none;
        }
        
        /* Product Listings */
        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .product-card {
            border: 1px solid #eee;
            border-radius: 8px;
            overflow: hidden;
            transition: transform 0.3s;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .product-image {
            height: 120px;
            background-color: var(--light-beige);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-light);
        }
        
        .product-image i {
            font-size: 40px;
        }
        
        .product-details {
            padding: 10px;
        }
        
        .product-name {
            font-weight: bold;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .product-price {
            color: var(--primary-green);
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .product-quantity {
            font-size: 12px;
            color: var(--text-medium);
            margin-bottom: 10px;
        }
        
        .product-tag {
            display: inline-block;
            background-color: var(--light-green);
            color: var(--dark-green);
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-right: 5px;
        }
        
        .product-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .product-actions button {
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .edit-btn {
            background-color: var(--primary-green);
            color: white;
        }
        
        .offline-indicator {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: var(--text-light);
            margin-top: 10px;
        }
        
        .offline-indicator .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #FFC107;
            margin-right: 5px;
        }
        
        /* Reservations Section */
        .reservation-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .reservation-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 8px;
        }
        
        .reservation-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--light-beige);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: var(--text-medium);
        }
        
        .reservation-details {
            flex: 1;
        }
        
        .reservation-product {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .reservation-info {
            font-size: 12px;
            color: var(--text-medium);
        }
        
        .reservation-actions {
            display: flex;
            gap: 5px;
        }
        
        .reservation-actions button {
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .approve-btn {
            background-color: var(--primary-green);
            color: white;
        }
        
        .reject-btn {
            background-color: #F44336;
            color: white;
        }
        
        /* Weather & Tips Section */
        .weather-card {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .weather-icon {
            font-size: 40px;
            color: #2196F3;
            margin-right: 15px;
        }
        
        .weather-details h3 {
            margin-bottom: 5px;
        }
        
        .weather-details p {
            font-size: 14px;
            color: var(--text-medium);
        }
        
        .alert-badge {
            display: inline-block;
            background-color: #FFC107;
            color: var(--text-dark);
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .farming-tip {
            background-color: var(--light-beige);
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .farming-tip h3 {
            margin-bottom: 10px;
            color: var(--earth-brown);
        }
        
        .farming-tip p {
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .play-audio {
            display: flex;
            align-items: center;
            color: var(--primary-green);
            font-size: 14px;
            cursor: pointer;
        }
        
        .play-audio i {
            margin-right: 5px;
        }
        
        /* Performance Tracker */
        .progress-container {
            margin-bottom: 20px;
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .progress-bar {
            height: 10px;
            background-color: #eee;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--primary-green);
            width: 75%;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .metric-card {
            background-color: var(--light-beige);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--dark-green);
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 12px;
            color: var(--text-medium);
        }
        
        .rating-stars {
            color: #FFC107;
            font-size: 18px;
            margin-top: 5px;
        }
        
        /* Quick Actions */
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .quick-action-btn {
            flex: 1;
            background-color: var(--primary-green);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .quick-action-btn i {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .quick-action-btn span {
            font-size: 12px;
        }
        
        /* Mobile Menu Toggle */
        .mobile-menu-toggle {
            display: none;
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 99;
            background-color: var(--primary-green);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
        }
        
        /* Responsive Adjustments */
        @media (max-width: 992px) {
            .sidebar {
                width: var(--sidebar-collapsed);
            }
            
            .sidebar:hover {
                width: var(--sidebar-width);
            }
            
            .main-content {
                margin-left: var(--sidebar-collapsed);
            }
            
            .sidebar:hover ~ .main-content {
                margin-left: var(--sidebar-width);
            }
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: var(--sidebar-width);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .mobile-menu-toggle {
                display: block;
            }
            
            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .quick-stats {
                width: 100%;
                justify-content: space-between;
            }
            
            .product-list {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }

        /* Fix sidebar behavior */
        .sidebar {
            width: var(--sidebar-width);
            background-color: white;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 100;
            display: flex;
            flex-direction: column;
            left: 0;
            top: 0;
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed);
        }

        .sidebar.collapsed .nav-menu span:not(.tooltip) {
            display: none;
        }

        .sidebar.collapsed .sidebar-header h1,
        .sidebar.collapsed .sidebar-header h3,
        .sidebar.collapsed .sidebar-header .farmer-name {
            display: none;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        .sidebar.collapsed ~ .main-content {
            margin-left: var(--sidebar-collapsed);
        }

        /* Loading indicator */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 18px;
            color: var(--primary-green);
        }

        /* Error message */
        .error {
            text-align: center;
            padding: 20px;
            background-color: #ffeeee;
            color: #e74c3c;
            border-radius: 8px;
            margin: 20px 0;
        }

        /* Content transitions */
        #dashboardContent {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Fix for mobile view */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 1000;
                top: 0;
                left: 0;
                height: 100vh;
                transition: transform 0.3s ease;
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                width: 100%;
            }
            
            .sidebar.collapsed ~ .main-content {
                margin-left: 0;
            }
        }

        /* Category Request Section */
        .category-request-section {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 25px;
            border: 1px solid #eee;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .section-header h3 {
            margin: 0;
            font-size: 16px;
            color: #333;
        }

        .approved-categories {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .category-tag {
            background-color: var(--primary-green);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            display: inline-block;
        }

        .pending-requests {
            margin-top: 15px;
        }

        .pending-category {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background-color: #fff;
            border-radius: 4px;
            margin-bottom: 8px;
            border: 1px solid #eee;
        }

        .status-badge {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 12px;
        }

        .status-badge.pending {
            background-color: #ffeeba;
            color: #856404;
        }

        .status-badge.approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-badge.rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #fff;
            margin: 10% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            animation: modalFadeIn 0.3s;
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 18px;
            color: #333;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        .modal-body {
            padding: 20px;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        body.modal-open {
            overflow: hidden;
        }

        /* Image Preview */
        .preview-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 0;
            width: 300px;
            max-width: 90%;
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            transform: translateY(0);
            opacity: 1;
        }

        .notification-content {
            display: flex;
            align-items: center;
            padding: 15px;
        }

        .notification-content i {
            margin-right: 10px;
            font-size: 20px;
        }

        .notification.success .notification-content i {
            color: var(--primary-green);
        }

        .notification.error .notification-content i {
            color: #e74c3c;
        }

        .notification .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            font-size: 14px;
        }

        /* Urgent Sales Section Styles */
        .urgent-sales-section {
            background-color: #FFF8E1;
            border-left: 4px solid #FF9800;
        }

        .urgent-sales-section h2 {
            color: #E65100;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .urgent-sales-section h2 i {
            color: #FF9800;
        }

        .urgent-sales-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .urgent-sale-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            position: relative;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .urgent-sale-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        }

        .urgent-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: #FF3D00;
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .price-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 5px 0;
        }

        .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 14px;
        }

        .reduced-price {
            color: #D32F2F;
            font-weight: bold;
            font-size: 18px;
        }

        .expiry-date {
            color: #E65100;
            font-size: 14px;
            margin: 5px 0;
        }

        /* Quick action button for urgent sales */
        .quick-action-btn:nth-child(2) {
            background-color: #FF9800;
        }

        .quick-action-btn:nth-child(2):hover {
            background-color: #F57C00;
        }

        /* Add these styles for the farmer profile in sidebar */
        .farmer-profile-summary {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 10px;
            border-bottom: 1px solid var(--border-color);
        }

        #farmerProfileImage {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--primary-green);
            margin-bottom: 10px;
        }

        .farmer-info {
            text-align: center;
        }

        .farmer-name {
            display: block;
            font-weight: 500;
            margin-top: 5px;
            color: var(--text-color);
        }

        /* Enhanced Quick Stats */
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--light-green);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-green);
            font-size: 20px;
        }

        .stat-content {
            flex: 1;
        }

        .stat-content .number {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-green);
            margin-bottom: 5px;
        }

        .stat-content .label {
            font-size: 14px;
            color: var(--text-medium);
            margin-bottom: 5px;
        }

        .change {
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .change.positive {
            color: #28a745;
        }

        .change.negative {
            color: #dc3545;
        }

        .rating-stars {
            color: #ffc107;
            font-size: 14px;
        }

        /* Performance Analytics Section */
        .performance-section {
            grid-column: 1 / -1;
            margin-bottom: 30px;
        }

        .time-filter select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: var(--text-dark);
            font-size: 14px;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .analytics-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .analytics-card h3 {
            margin-bottom: 15px;
            color: var(--primary-green);
            font-size: 16px;
        }

        .analytics-card canvas {
            width: 100%;
            height: 200px;
        }

        /* Top Products List */
        .top-products-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .top-product-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .product-rank {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-green);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .product-info {
            flex: 1;
        }

        .product-name {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .product-stats {
            display: flex;
            gap: 10px;
            font-size: 12px;
            color: var(--text-medium);
        }

        .product-trend {
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .product-trend.up {
            color: #28a745;
        }

        .product-trend.down {
            color: #dc3545;
        }

        /* Customer Metrics */
        .customer-metrics {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: var(--text-medium);
            font-size: 14px;
        }

        .metric-value {
            font-weight: 600;
            color: var(--primary-green);
            font-size: 16px;
        }

        /* Recent Activity Section */
        .recent-activity-section {
            grid-column: 1 / -1;
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid var(--primary-green);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .activity-icon.sale {
            background: #28a745;
        }

        .activity-icon.reservation {
            background: #007bff;
        }

        .activity-icon.listing {
            background: var(--primary-green);
        }

        .activity-icon.urgent_sale {
            background: #dc3545;
        }

        .activity-icon.review {
            background: #ffc107;
        }

        .activity-icon.message {
            background: #6f42c1;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .activity-description {
            color: var(--text-medium);
            font-size: 14px;
            margin-bottom: 4px;
        }

        .activity-time {
            color: var(--text-light);
            font-size: 12px;
        }

        .activity-value {
            font-weight: 600;
            color: var(--primary-green);
            font-size: 16px;
        }

        .no-activity {
            text-align: center;
            color: var(--text-medium);
            padding: 40px;
            font-style: italic;
        }

        /* Error Container Styles */
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px;
        }

        .error-icon {
            font-size: 64px;
            color: #dc3545;
            margin-bottom: 20px;
        }

        .error-container h3 {
            color: var(--text-dark);
            margin-bottom: 15px;
            font-size: 24px;
        }

        .error-container p {
            color: var(--text-medium);
            margin-bottom: 10px;
            font-size: 16px;
        }

        .error-details {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            margin: 15px 0;
        }

        .error-actions {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .error-actions button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .error-actions .btn-primary {
            background: var(--primary-green);
            color: white;
        }

        .error-actions .btn-primary:hover {
            background: var(--dark-green);
            transform: translateY(-1px);
        }

        .error-actions .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .error-actions .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-1px);
        }

        /* Loading Styles */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            font-size: 16px;
            color: var(--text-medium);
        }

        .loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-green);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Quick Action Buttons */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            padding: 20px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            color: white;
        }

        .action-btn i {
            font-size: 24px;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #2c5530, #4CAF50);
        }

        .action-btn.primary:hover {
            background: linear-gradient(135deg, #1e3a21, #388e3c);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
        }

        .action-btn.secondary {
            background: linear-gradient(135deg, #ff5722, #f44336);
        }

        .action-btn.secondary:hover {
            background: linear-gradient(135deg, #e64a19, #d32f2f);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
        }

        .action-btn.tertiary {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .action-btn.tertiary:hover {
            background: linear-gradient(135deg, #1976D2, #1565C0);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .action-btn.quaternary {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
        }

        .action-btn.quaternary:hover {
            background: linear-gradient(135deg, #7B1FA2, #6A1B9A);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
        }

        /* Content Placeholder Styles */
        .content-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 20px;
        }

        .placeholder-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #2c5530, #4CAF50);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .placeholder-icon i {
            font-size: 32px;
            color: white;
        }

        .content-placeholder h3 {
            color: #2c5530;
            margin-bottom: 10px;
            font-size: 24px;
        }

        .content-placeholder p {
            color: #666;

/* ========================================
   FARMER PAGE SPECIFIC STYLES
   ======================================== */

/* Form Styles */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.product-form {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #27ae60;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Enhanced Button Styles */
.btn-primary, .btn-secondary, .btn-success, .btn-danger {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #27ae60;
    color: white;
}

.btn-primary:hover {
    background: #219a52;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Reservations Page */
.reservations-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 600;
    color: #333;
}

.filter-group select {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 6px;
    min-width: 150px;
}

.reservation-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #27ae60;
}

.reservation-card.pending {
    border-left-color: #ffc107;
}

.reservation-card.approved {
    border-left-color: #28a745;
}

.reservation-card.rejected {
    border-left-color: #dc3545;
}

.reservation-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.reservation-info h4 {
    margin: 0 0 5px 0;
    color: #333;
}

.buyer-name {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.approved {
    background: #d4edda;
    color: #155724;
}

.status-badge.rejected {
    background: #f8d7da;
    color: #721c24;
}

.reservation-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
}

.detail-item .label {
    font-weight: 600;
    color: #666;
}

.detail-item .value {
    color: #333;
}
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .placeholder-actions {
            margin-top: 20px;
        }

        .placeholder-actions .btn-primary {
            background: #2c5530;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .placeholder-actions .btn-primary:hover {
            background: #1e3a21;
        }

        /* Mobile Responsiveness for Analytics */
        @media (max-width: 768px) {
            .analytics-grid {
                grid-template-columns: 1fr;
            }

            .quick-stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .stat-item {
                padding: 15px;
                gap: 10px;
            }

            .stat-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .stat-content .number {
                font-size: 20px;
            }

            .activity-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .activity-icon {
                width: 32px;
                height: 32px;
                font-size: 14px;
            }

            .error-container {
                padding: 40px 15px;
                margin: 10px;
            }

            .error-icon {
                font-size: 48px;
            }

            .error-container h3 {
                font-size: 20px;
            }

            .error-actions {
                flex-direction: column;
                width: 100%;
            }

            .error-actions button {
                width: 100%;
                justify-content: center;
            }
        }

        /* Messaging Modal Styles */
        .messaging-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2000;
            transition: all 0.3s ease;
        }

        .messaging-modal.hidden {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
        }

        .messaging-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .messaging-container {
            background: var(--white);
            border-radius: 15px;
            width: 100%;
            max-width: 1000px;
            height: 80vh;
            max-height: 600px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
        }

        .messaging-header {
            background: var(--primary-green);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .messaging-header h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
        }

        .btn-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .messaging-content {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* Conversations Panel */
        .conversations-panel {
            width: 350px;
            border-right: 1px solid #eee;
            display: flex;
            flex-direction: column;
            background: #f8f9fa;
        }

        .conversations-header {
            padding: 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .conversations-header h4 {
            margin: 0;
            color: var(--text-dark);
            font-size: 16px;
        }

        .btn-new-message {
            background: var(--primary-green);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .btn-new-message:hover {
            background: var(--dark-green);
        }

        .conversations-search {
            padding: 15px;
            border-bottom: 1px solid #eee;
        }

        .conversations-search input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
        }

        .conversations-search input:focus {
            border-color: var(--primary-green);
        }

        .conversations-list {
            flex: 1;
            overflow-y: auto;
        }

        .conversation-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .conversation-item:hover {
            background: #e8f5e8;
        }

        .conversation-item.active {
            background: var(--light-green);
            border-left: 4px solid var(--primary-green);
        }

        .conversation-avatar {
            position: relative;
            margin-right: 12px;
        }

        .conversation-avatar img {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            object-fit: cover;
        }

        .status-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .status-indicator.online {
            background: #4CAF50;
        }

        .status-indicator.offline {
            background: #ccc;
        }

        .conversation-info {
            flex: 1;
            min-width: 0;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .conversation-name {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 14px;
        }

        .conversation-time {
            font-size: 12px;
            color: var(--text-light);
        }

        .conversation-preview {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .last-message {
            font-size: 13px;
            color: var(--text-medium);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 200px;
        }

        .unread-badge {
            background: #ff4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 11px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* Chat Panel */
        .chat-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--white);
        }

        .chat-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
        }

        .chat-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chat-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--light-green);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-green);
        }

        .chat-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .chat-details {
            flex: 1;
        }

        .chat-name {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 16px;
            margin-bottom: 2px;
        }

        .chat-status {
            font-size: 12px;
            color: var(--text-light);
        }

        .btn-options {
            background: none;
            border: none;
            color: var(--text-medium);
            font-size: 16px;
            cursor: pointer;
            padding: 8px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .btn-options:hover {
            background: var(--light-green);
            color: var(--primary-green);
        }

        /* Messages Container */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .no-conversation {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: var(--text-light);
            text-align: center;
        }

        .no-conversation i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #ddd;
        }

        .no-conversation p {
            font-size: 16px;
            margin: 0;
        }

        /* Message Styles */
        .message {
            display: flex;
            margin-bottom: 15px;
            align-items: flex-end;
        }

        .message.own {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            background: var(--white);
            padding: 12px 16px;
            border-radius: 18px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .message.own .message-content {
            background: var(--primary-green);
            color: white;
        }

        .message-text {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 5px;
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
        }

        .message-status {
            margin-left: 8px;
            color: var(--text-light);
            font-size: 12px;
        }

        .message-status.read {
            color: var(--primary-green);
        }

        /* Message Input */
        .message-input-container {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            background: var(--white);
        }

        .message-input {
            display: flex;
            align-items: center;
            gap: 10px;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 8px 15px;
            border: 1px solid #eee;
        }

        .message-input input {
            flex: 1;
            border: none;
            background: none;
            outline: none;
            font-size: 14px;
            padding: 8px 0;
        }

        .btn-send {
            background: var(--primary-green);
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.3s ease;
        }

        .btn-send:hover:not(:disabled) {
            background: var(--dark-green);
        }

        .btn-send:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* No Conversations State */
        .no-conversations {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: var(--text-light);
            text-align: center;
            padding: 20px;
        }

        .no-conversations i {
            font-size: 36px;
            margin-bottom: 10px;
            color: #ddd;
        }

        .no-conversations p {
            margin: 5px 0;
            font-size: 14px;
        }

        .no-conversations small {
            font-size: 12px;
            opacity: 0.8;
        }

        /* Loading State */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: var(--text-light);
            font-size: 14px;
        }

        /* Responsive Design for Messaging */
        @media (max-width: 768px) {
            .messaging-container {
                width: 100%;
                height: 100vh;
                max-height: none;
                border-radius: 0;
            }

            .messaging-content {
                flex-direction: column;
            }

            .conversations-panel {
                width: 100%;
                height: 40%;
                border-right: none;
                border-bottom: 1px solid #eee;
            }

            .chat-panel {
                height: 60%;
            }

            .conversation-item {
                padding: 12px 15px;
            }

            .conversation-avatar img {
                width: 40px;
                height: 40px;
            }

            .message-content {
                max-width: 85%;
            }
        }

        @media (max-width: 480px) {
            .messaging-overlay {
                padding: 0;
            }

            .messaging-header {
                padding: 15px;
            }

            .messaging-header h3 {
                font-size: 18px;
            }

            .conversations-header {
                padding: 12px 15px;
            }

            .conversations-search {
                padding: 12px 15px;
            }

            .message-content {
                max-width: 90%;
                padding: 10px 14px;
            }

            .message-text {
                font-size: 13px;
            }
        }

        /* Header Responsive */
        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }

            .header-right {
                width: 100%;
                justify-content: space-between;
            }

            .profile-btn span {
                display: none;
            }

            .dropdown-menu {
                right: auto;
                left: 0;
                min-width: 150px;
            }
        }

        @media (max-width: 480px) {
            .dashboard-header {
                padding: 12px;
            }

            .header-left h2 {
                font-size: 20px;
            }

            .header-btn {
                padding: 8px;
            }

            .header-btn i {
                font-size: 16px;
            }

            .profile-btn {
                padding: 6px 10px;
            }

            .profile-btn img {
                width: 25px;
                height: 25px;
            }
        }
